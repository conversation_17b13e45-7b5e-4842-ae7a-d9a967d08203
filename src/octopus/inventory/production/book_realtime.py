from functools import partial
from typing import List

from src.octopus.applications.proxies.enums import (
    Binance<PERSON>Y5<PERSON><PERSON>znerEu,
    BinanceNY5WebShare,
    BullishNY5WebShareEu,
    BybitNY5HetznerEu,
    BybitProdWebShareEu,
    CoinbaseIntNY5HetznerEu,
    CoinbaseIntProdHetznerEu,
    GeminiHetznerEu,
    GeminiNY5HetznerUs,
    <PERSON>tznerUs,
    SmartproxyNY5,
)
from src.octopus.applications.proxies.inventory import HetznerE<PERSON>
from src.octopus.inventory.api_keys import BINANCE_US_API_KEY, CME_API_KEY
from src.octopus.inventory.common import (
    BETTERMENT_MASTER_INSTRUMENT_LIST,
    BINANCE_BUSY_FUTURE_MARKETS,
    BINANCE_BUSY_SPOT_MARKETS,
    BINANCE_DB_FUTURE_MARKETS,
    BITFINEX_BUSY_SPOT_MARKETS,
    COINBASE_BUSY_SPOT_MARKETS_1,
    COINBASE_BUSY_SPOT_MARKETS_2,
    COINBASE_BUSY_SPOT_MARKETS_3,
    HUOBI_BUSY_FUTURE_MARKETS,
    HUOBI_BUSY_SPOT_MARKETS,
    KRAKEN_BUSY_FUTURE_MARKETS,
    KUCOIN_BUSY_FUTURE_MARKETS_1,
    KUCOIN_BUSY_FUTURE_MARKETS_2,
    KUCOIN_BUSY_FUTURE_MARKETS_3,
    KUCOIN_BUSY_FUTURE_MARKETS_4,
    KUCOIN_BUSY_FUTURE_MARKETS_5,
    MEXC_BUSY_SPOT_MARKETS_BATCH_1,
    MEXC_BUSY_SPOT_MARKETS_BATCH_2,
    MEXC_BUSY_SPOT_MARKETS_BATCH_3,
    MEXC_BUSY_SPOT_MARKETS_BATCH_4,
    MEXC_BUSY_SPOT_MARKETS_BATCH_5,
)
from src.octopus.inventory.types import (
    FHGroup,
    Scraper,
    feed_handler_range_deployment_constructor,
    futures_http_scraper,
    futures_streaming_scraper,
    option_streaming_scraper,
    split_deployments_in_batches,
    split_list_in_batches,
    spot_http_scraper,
    spot_streaming_scraper,
    option_http_scraper,
)

BYBIT_PROXIES = [[BybitNY5HetznerEu.BOOK_REALTIME], [BybitProdWebShareEu.BOOK_REALTIME]]
BULLISH_PROXIES = [[BullishNY5WebShareEu.BOOK_TICK_BY_TICK]]
SPOT_HTTP = [
    spot_http_scraper(
        "Binance.US",
        deployment_flags={
            "entrypoint_args": {"api_params": BINANCE_US_API_KEY, "instruments": ["btc-usd"], "poll_interval": 0},
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "bitFlyer",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd", "btc-jpy", "eth-jpy"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.1,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK], [SmartproxyNY5.BOOK_REALTIME]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "Bybit",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usdt", "eth-usdt"],
                "rate_limit_multiplier": 0.15,
                "proxy_groups": BYBIT_PROXIES,
                "session_ttl": 600,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "Crypto.com",
        deployment_flags={
            "entrypoint_args": {
                "instruments": BETTERMENT_MASTER_INSTRUMENT_LIST,
                "poll_interval": 0,
                "book_depth": 100,
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": None,
                "rate_limit_multiplier": 0.25,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "Gate.io",
        deployment_flags={
            "entrypoint_args": {
                "instruments": BETTERMENT_MASTER_INSTRUMENT_LIST,
                "poll_interval": 0,
                "book_depth": 100,
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": None,
                "rate_limit_multiplier": 0.25,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper("itBit", group=FHGroup.GROUP_1),  # don't remove, ws fh is loosing data
    spot_http_scraper(
        "Kraken",
        deployment_flags={"entrypoint_args": {"instruments": ["btc-usd", "eth-usd"]}},
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "KuCoin",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usdt",
                    "aave-usdt",
                    "amp-usdt",
                    "ankr-usdt",
                    "audio-usdt",
                    "axs-usdt",
                    "bat-usdt",
                    "bch-usdc",
                    "bch-usdt",
                    "bnt-usdt",
                    "btc-usdc",
                    "btc-usdt",
                    "comp-usdt",
                    "crv-usdt",
                    "doge-usdc",
                    "doge-usdt",
                    "dot-usdc",
                    "dot-usdt",
                    "enj-usdt",
                    "eth-usdc",
                    "eth-usdt",
                    "fil-usdt",
                    "ftm-usdc",
                    "ftm-usdt",
                    "grt-usdt",
                    "link-usdc",
                    "link-usdt",
                    "lpt-usdt",
                    "lrc-usdt",
                    "ltc-usdc",
                    "ltc-usdt",
                    "luna-usdc",
                    "luna-usdt",
                    "mana-usdt",
                    "matic-usdc",
                    "matic-usdt",
                    "mkr-usdt",
                    "qnt-usdt",
                    "sand-usdt",
                    "skl-usdt",
                    "slp-usdt",
                    "snx-usdt",
                    "sol-usdc",
                    "sol-usdt",
                    "sushi-usdt",
                    "uni-usdt",
                    "xtz-usdt",
                    "yfi-usdt",
                    "zec-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": None,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "OKEx BatchA",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usdc",
                    "1inch-usdt",
                    "aave-usdc",
                    "aave-usdt",
                    "axs-usdc",
                    "axs-usdt",
                    "bat-usdc",
                    "bat-usdt",
                    "bch-usdc",
                    "bch-usdt",
                    "bnt-usdt",
                    "btc-usdc",
                    "btc-usdt",
                    "comp-usdc",
                    "comp-usdt",
                    "crv-usdc",
                    "crv-usdt",
                    "doge-usdc",
                    "doge-usdt",
                    "dot-usdc",
                    "dot-usdt",
                    "enj-usdt",
                    "eth-usdc",
                    "eth-usdt",
                    "fil-usdc",
                    "fil-usdt",
                    "ftm-usdc",
                    "ftm-usdt",
                    "grt-usdc",
                    "grt-usdt",
                    "link-usdc",
                    "link-usdt",
                ],
                "poll_interval": 7,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME]],
                "rate_limit_multiplier": 0.15,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "OKEx BatchB",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "lpt-usdt",
                    "lrc-usdc",
                    "lrc-usdt",
                    "ltc-usdc",
                    "ltc-usdt",
                    "luna-usdc",
                    "luna-usdt",
                    "mana-usdc",
                    "mana-usdt",
                    "matic-usdc",
                    "matic-usdt",
                    "mkr-usdt",
                    "sand-usdc",
                    "sand-usdt",
                    "skl-usdt",
                    "slp-usdt",
                    "snx-usdc",
                    "snx-usdt",
                    "sol-usdc",
                    "sol-usdt",
                    "sushi-usdc",
                    "sushi-usdt",
                    "uni-usdc",
                    "uni-usdt",
                    "xtz-usdc",
                    "xtz-usdt",
                    "yfi-usdc",
                    "yfi-usdt",
                    "zec-usdc",
                    "zec-usdt",
                    "zrx-usdt",
                ],
                "poll_interval": 7,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME]],
                "rate_limit_multiplier": 0.15,
            }
        },
        group=FHGroup.GROUP_1,
    ),
]
SPOT_STREAMING = [
    spot_streaming_scraper(
        "Binance BatchA",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usdt",
                    "aave-usdt",
                    "amp-usdt",
                    "ankr-usdt",
                    "audio-usdt",
                    "axs-usdt",
                    "bat-usdt",
                    "bch-usdc",
                    "bch-usdt",
                    "bnb-usdt",
                    "bnt-usdt",
                    "btc-busd",
                    "btc-usdc",
                    "btc-usdt",
                    "busd-usdt",
                    "comp-usdt",
                    "crv-usdt",
                    "doge-usdt",
                    "dot-usdt",
                    "enj-usdt",
                    "eth-btc",
                    "eth-usdc",
                    "eth-usdt",
                    "eos-usdt",
                    "fil-usdt",
                ],
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "rate_limit_multiplier": 0.25,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Binance BatchB",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "ftm-usdt",
                    "grt-usdt",
                    "link-usdc",
                    "link-usdt",
                    "lpt-usdt",
                    "lrc-usdt",
                    "ltc-usdc",
                    "ltc-usdt",
                    "luna-usdt",
                    "mana-usdt",
                    "matic-usdt",
                    "mkr-usdt",
                    "qnt-usdt",
                    "sand-usdt",
                    "skl-usdt",
                    "slp-usdt",
                    "snx-usdt",
                    "sol-usdc",
                    "sol-usdt",
                    "sushi-usdt",
                    "uni-usdt",
                    "xtz-usdt",
                    "yfi-usdt",
                    "zec-usdc",
                    "zec-usdt",
                    "zrx-usdt",
                ],
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "rate_limit_multiplier": 0.25,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Binance Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 50,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[BinanceNY5WebShare.BOOK_REALTIME, BinanceNY5HetznerEu.BOOK_REALTIME]],
                "instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Binance",
        [f"{elem:04d}" for elem in range(0, 1419, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 30,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.01,
                "proxy_groups": [[BinanceNY5WebShare.BOOK_REALTIME, BinanceNY5HetznerEu.BOOK_REALTIME]],
                "exclude_instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
            "mem_limit": "9g",
        },
    ),
    spot_streaming_scraper(
        "Binance.US",
        deployment_flags={
            "entrypoint_args": {
                "api_params": BINANCE_US_API_KEY,
                "instruments": [
                    "1inch-usd",
                    "1inch-usdt",
                    "aave-usd",
                    "aave-usdt",
                    "amp-usd",
                    "ankr-usd",
                    "audio-usd",
                    "audio-usdt",
                    "axs-usd",
                    "axs-usdt",
                    "bat-usd",
                    "bat-usdt",
                    "bch-usd",
                    "bch-usdt",
                    "bnt-usd",
                    "bnt-usdt",
                    "btc-usd",
                    "btc-usdc",
                    "btc-usdt",
                    "comp-usd",
                    "comp-usdt",
                    "crv-usd",
                    "crv-usdt",
                    "doge-usd",
                    "doge-usdt",
                    "dot-usd",
                    "dot-usdt",
                    "enj-usd",
                    "enj-usdt",
                    "eth-usd",
                    "eth-usdc",
                    "eth-usdt",
                    "fil-usd",
                    "fil-usdt",
                    "ftm-usd",
                    "ftm-usdt",
                    "grt-usd",
                    "grt-usdt",
                    "link-usd",
                    "link-usdt",
                    "lpt-usd",
                    "lpt-usdt",
                    "lrc-usd",
                    "lrc-usdt",
                    "ltc-usd",
                    "ltc-usdt",
                    "mana-usd",
                    "mana-usdt",
                    "matic-usd",
                    "matic-usdt",
                    "mkr-usd",
                    "mkr-usdt",
                    "skl-usd",
                    "skl-usdt",
                    "slp-usd",
                    "slp-usdt",
                    "snx-usd",
                    "snx-usdt",
                    "sol-usd",
                    "sol-usdt",
                    "sushi-usd",
                    "sushi-usdt",
                    "uni-usd",
                    "uni-usdt",
                    "xtz-usd",
                    "yfi-usd",
                    "yfi-usdt",
                    "zec-usd",
                    "zec-usdt",
                    "zrx-usd",
                    "zrx-usdt",
                ],
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Binance.US All",
        [f"{elem:03d}" for elem in range(0, 179, 80)],
        deployment_flags={
            "entrypoint_args": {
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
    ),
    spot_streaming_scraper(
        "Bitbank All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_2,
    ),
    spot_streaming_scraper(
        "Bitfinex Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK]],
                "instruments": BITFINEX_BUSY_SPOT_MARKETS,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_3),
        "Bitfinex",
        [f"{elem:03d}" for elem in range(0, 312, 40)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.2,
                "exclude_instruments": BITFINEX_BUSY_SPOT_MARKETS,
            },
        },
    ),
    spot_streaming_scraper(
        "Bitfinex BatchA",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 30,
                "instruments": [
                    "1inch-usd",
                    "1inch-usdt",
                    "aave-usd",
                    "aave-usdt",
                    "axs-usd",
                    "axs-usdt",
                    "bat-usd",
                    "bat-usdt",
                    "bnt-usd",
                    "btc-usdt",
                    "comp-usd",
                    "comp-usdt",
                    "crv-usd",
                    "crv-usdt",
                    "doge-usd",
                    "doge-usdt",
                    "dot-usd",
                    "dot-usdt",
                    "enj-usd",
                    "eth-usdt",
                    "fil-usd",
                    "fil-usdt",
                    "ftm-usd",
                    "ftm-usdt",
                    "grt-usd",
                    "grt-usdt",
                    "shib-usd",
                    "ton-usd",
                    "leo-usd",
                ],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bitfinex BatchB",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 30,
                "instruments": [
                    "link-usd",
                    "link-usdt",
                    "lrc-usd",
                    "ltc-usd",
                    "ltc-usdt",
                    "luna-usd",
                    "luna-usdt",
                    "mana-usd",
                    "matic-usd",
                    "matic-usdt",
                    "mkr-usd",
                    "mkr-usdt",
                    "sand-usd",
                    "sand-usdt",
                    "snx-usd",
                    "snx-usdt",
                    "sol-usd",
                    "sol-usdt",
                    "sushi-usd",
                    "sushi-usdt",
                    "uni-usd",
                    "uni-usdt",
                    "xtz-usd",
                    "xtz-usdt",
                    "yfi-usd",
                    "yfi-usdt",
                    "zec-usd",
                    "zrx-usd",
                    "xmr-usd",
                    "apt-usd",
                    "hbar-usd",
                    "euroc-usdc",
                ],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bitfinex BTC-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd"],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bitfinex ETH-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["eth-usd"],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bitfinex NEXO-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["nexo-usd"],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "bitFlyer All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.20,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_2,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Bitstamp",
        [f"{elem:03d}" for elem in range(0, 245, 60)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
    ),
    spot_streaming_scraper(
        "Bitstamp",
        deployment_flags={
            "entrypoint_args": {
                "instruments": BETTERMENT_MASTER_INSTRUMENT_LIST,
                "markets_per_producer": 5,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_1),
        "Bullish",
        ["000", "050"],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.02,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_1),
        "Bullish DB",
        ["000", "050"],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": None,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.02,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
    ),
    spot_streaming_scraper(
        "Bybit BatchA",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usdt",
                    "aave-usdt",
                    "ach-usdt",
                    "ada-usdt",
                    "agld-usdt",
                    "algo-btc",
                    "algo-usdt",
                    "amp-usdt",
                    "ankr-usdt",
                    "ape-usdt",
                    "atom-usdt",
                    "ava-usdt",
                    "avax-usdt",
                    "axs-usdt",
                    "bat-usdt",
                    "bch-usdt",
                    "bit-btc",
                    "bit-usdc",
                    "bit-usdt",
                    "bnb-usdt",
                    "bnt-usdt",
                    "boba-usdt",
                    "btc-dai",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bybit BatchB",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "btc-usdc",
                    "btc-usdt",
                    "btc-ust",
                    "btc3l-usdt",
                    "btc3s-usdt",
                    "btg-usdt",
                    "bttc-usdt",
                    "busd-usdt",
                    "c98-usdt",
                    "cake-usdt",
                    "caps-usdt",
                    "cbx-usdt",
                    "cel-usdt",
                    "celo-usdt",
                    "chz-usdt",
                    "comp-usdt",
                    "crv-usdt",
                    "dai-usdt",
                    "dash-usdt",
                    "dcr-usdt",
                    "dfl-usdt",
                    "dgb-usdt",
                    "doge-usdc",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bybit BatchC",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "doge-usdt",
                    "dot-btc",
                    "dot-usdc",
                    "dot-usdt",
                    "dydx-usdt",
                    "egld-usdt",
                    "elt-usdt",
                    "enj-usdt",
                    "ens-usdt",
                    "eos-usdt",
                    "etc-usdt",
                    "eth-btc",
                    "eth-dai",
                    "eth-usdc",
                    "eth-usdt",
                    "eth-ust",
                    "fida-usdt",
                    "fil-usdt",
                    "flow-usdt",
                    "ftm-usdt",
                    "ftt-usdt",
                    "gal-usdt",
                    "gala-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bybit BatchD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "gas_gasdao-usdt",
                    "gas-usdt",
                    "gene-usdt",
                    "gm-usdt",
                    "gods-usdt",
                    "grt-usdt",
                    "gst-usdt",
                    "hbar-usdt",
                    "hero-usdt",
                    "hnt-usdt",
                    "icp-usdt",
                    "icx-usdt",
                    "imx-usdt",
                    "insur-usdt",
                    "jasmy-usdt",
                    "jst-usdt",
                    "klay-usdt",
                    "kma-usdt",
                    "krl-usdt",
                    "ksm-usdt",
                    "ldo-usdt",
                    "lfw-usdt",
                    "link-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bybit BatchE",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "looks-usdt",
                    "lrc-usdt",
                    "ltc-btc",
                    "ltc-usdc",
                    "ltc-usdt",
                    "luna-btc",
                    "luna-usdc",
                    "luna-usdt",
                    "mana-btc",
                    "mana-usdc",
                    "mana-usdt",
                    "matic-btc",
                    "matic-usdc",
                    "matic-usdt",
                    "mina-usdt",
                    "mix-usdt",
                    "mkr-usdt",
                    "movr-usdt",
                    "mx-usdt",
                    "near-usdt",
                    "nexo-usdt",
                    "nft-usdt",
                    "nu-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bybit BatchF",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "omg-usdt",
                    "one-usdt",
                    "paxg-usdt",
                    "perp-usdt",
                    "plt-usdt",
                    "psp-usdt",
                    "ptu-usdt",
                    "qnt-usdt",
                    "qtum-usdt",
                    "raca-usdt",
                    "real-usdt",
                    "ren-usdt",
                    "rndr-usdt",
                    "rune-usdt",
                    "rvn-usdt",
                    "sand-btc",
                    "sand-usdc",
                    "sand-usdt",
                    "sc-usdt",
                    "scrt-usdt",
                    "sfund-usdt",
                    "shib-usdt",
                    "shill-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bybit BatchG",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "sis-usdt",
                    "slp-usdt",
                    "snx-usdt",
                    "sol-btc",
                    "sol-usdc",
                    "sol-usdt",
                    "son-usdt",
                    "sos-usdt",
                    "spell-usdt",
                    "srm-usdt",
                    "stx-usdt",
                    "sun-usdt",
                    "sushi-usdt",
                    "tel-usdt",
                    "theta-usdt",
                    "tribe-usdt",
                    "trvl-usdt",
                    "trx-usdt",
                    "uma-usdt",
                    "uni-usdt",
                    "usdc-usdt",
                    "ust-usdt",
                    "waves-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Bybit BatchH",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "wbtc-usdt",
                    "woo-usdt",
                    "xdc-usdt",
                    "xec-usdt",
                    "xem-usdt",
                    "xlm-btc",
                    "xlm-usdc",
                    "xlm-usdt",
                    "xrp-btc",
                    "xrp-usdc",
                    "xrp-usdt",
                    "xtz-usdt",
                    "xym-usdt",
                    "yfi-usdt",
                    "zec-usdt",
                    "zen-usdt",
                    "zil-usdt",
                    "zrx-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.01,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Bybit All",
        [f"{elem:03d}" for elem in range(0, 635, 80)],
        deployment_flags={
            "entrypoint_args": {
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.04,
                "proxy_groups": BYBIT_PROXIES,
            }
        },
    ),
    # spot_streaming_scraper(
    #     "CEX.IO",
    #     deployment_flags={
    #         "entrypoint_args": {
    #             "streaming_api_params": CEX_IO_API_KEY,
    #             "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
    #             "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
    #             "postgres_out": None,
    #             "storage_interval": None,
    #             "websocket_out": None,
    #             "book_depth": 30000,
    #             "rate_limit_multiplier": 0.05,
    #             "markets_per_producer": 30,
    #         },
    #     },
    #     group=FHGroup.GROUP_4,
    # ),
    # spot_streaming_scraper(
    #     "CEX.IO DB",
    #     deployment_flags={
    #         "entrypoint_args": {
    #             "instruments": [
    #                 "1inch-usd",
    #                 "aave-usd",
    #                 "aave-usdt",
    #                 "audio-usd",
    #                 "axs-usd",
    #                 "bat-usd",
    #                 "bat-usdt",
    #                 "bch-usd",
    #                 "bch-usdt",
    #                 "bnt-usd",
    #                 "bnt-usdt",
    #                 "btc-usd",
    #                 "btc-usdt",
    #                 "comp-usd",
    #                 "comp-usdt",
    #                 "crv-usd",
    #                 "crv-usdt",
    #                 "doge-usd",
    #                 "dot-usd",
    #                 "dot-usdt",
    #                 "eth-usd",
    #                 "eth-usdt",
    #                 "fil-usd",
    #                 "ftm-usd",
    #                 "grt-usd",
    #                 "link-usd",
    #                 "link-usdt",
    #                 "lrc-usd",
    #                 "ltc-usd",
    #                 "ltc-usdt",
    #                 "luna-usd",
    #                 "mana-usd",
    #                 "matic-usd",
    #                 "matic-usdt",
    #                 "mkr-usd",
    #                 "qnt-usd",
    #                 "sand-usd",
    #                 "snx-usd",
    #                 "snx-usdt",
    #                 "sol-usd",
    #                 "sushi-usd",
    #                 "sushi-usdt",
    #                 "uni-usd",
    #                 "uni-usdt",
    #                 "xtz-usd",
    #                 "xtz-usdt",
    #                 "yfi-usd",
    #                 "yfi-usdt",
    #                 "zrx-usd",
    #                 "zrx-usdt",
    #             ],
    #             "streaming_api_params": CEX_IO_API_KEY,
    #         },
    #     },
    #     group=FHGroup.GROUP_1,
    # ),
    *split_deployments_in_batches(
        spot_streaming_scraper,
        "Coinbase",
        instrument_batches=[
            *split_list_in_batches(
                5,
                [
                    "1inch-usd",
                    "aave-usd",
                    "amp-usd",
                    "ankr-usd",
                    "axs-usd",
                    "axs-usdt",
                    "bat-usd",
                    "bch-usd",
                    "btc-usdt",
                    "crv-usd",
                    "doge-usd",
                    "doge-usdt",
                    "dot-usd",
                    "dot-usdt",
                    "enj-usd",
                    "enj-usdt",
                    "eth-usdt",
                    "eos-usd",
                    "fil-usd",
                    "grt-usd",
                    "link-usd",
                    "link-usdt",
                    "lpt-usd",
                    "lrc-usd",
                    "lrc-usdt",
                    "ltc-usd",
                    "mana-usd",
                    "matic-usd",
                    "matic-usdt",
                    "mco2-usd",
                    "mco2-usdt",
                    "qnt-usd",
                    "qnt-usdt",
                    "sand-usd",
                    "sand-usdt",
                    "skl-usd",
                    "sol-usd",
                    "sol-usdt",
                    "xtz-usd",
                    "zec-usd",
                    "atom-usd",
                    "bal-usd",
                    "bnt-usd",
                    "snx-usd",
                    "sushi-usd",
                ],
            ),
            ["uma-usd", "uni-usd", "yfi-usd", "zrx-usd", "comp-usd", "dash-usd", "mkr-usd"],
        ],
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Coinbase BTC-USD",
        deployment_flags={"entrypoint_args": {"instruments": ["btc-usd"]}},
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Coinbase ETH-BTC",
        deployment_flags={"entrypoint_args": {"instruments": ["eth-btc"]}},
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Coinbase ETH-USD",
        deployment_flags={"entrypoint_args": {"instruments": ["eth-usd"]}},
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Coinbase Busy1",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_1,
            }
        },
        group=FHGroup.GROUP_2,
    ),
    spot_streaming_scraper(
        "Coinbase Busy2",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_2,
            }
        },
        group=FHGroup.GROUP_2,
    ),
    spot_streaming_scraper(
        "Coinbase Busy3",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_3,
            }
        },
        group=FHGroup.GROUP_2,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Coinbase",
        [f"{elem:04d}" for elem in range(0, 417, 10)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "exclude_instruments": [
                    *COINBASE_BUSY_SPOT_MARKETS_1,
                    *COINBASE_BUSY_SPOT_MARKETS_2,
                    *COINBASE_BUSY_SPOT_MARKETS_3,
                ],
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_1),
        "Crypto.com All",
        [f"{elem:03d}" for elem in range(0, 635, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.03,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "ErisX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
                "compress_books_before_send": True,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "ErisX DB",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
                "compress_books_before_send": True,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Gate.io",
        [f"{elem:04d}" for elem in range(0, 3222, 400)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK]],
                "rate_limit_multiplier": 0.25,
            }
        },
    ),
    spot_streaming_scraper(
        "Gemini BatchA",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usd",
                    "aave-usd",
                    "amp-usd",
                    "ankr-usd",
                    "audio-usd",
                    "axs-usd",
                    "bat-usd",
                    "bch-usd",
                    "bnt-usd",
                    "comp-usd",
                    "crv-usd",
                    "doge-usd",
                    "dot-usd",
                    "enj-usd",
                    "fil-usd",
                    "ftm-usd",
                    "grt-usd",
                    "link-usd",
                    "lpt-usd",
                    "lrc-usd",
                    "ltc-usd",
                    "luna-usd",
                    "mana-usd",
                    "matic-usd",
                    "mco2-usd",
                    "mkr-usd",
                    "qnt-usd",
                    "sand-usd",
                    "skl-usd",
                    "slp-usd",
                    "snx-usd",
                    "sol-usd",
                    "sushi-usd",
                    "uni-usd",
                    "xtz-usd",
                    "yfi-usd",
                    "zec-usd",
                    "zrx-usd",
                ],
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.1,
                "proxy_groups": [[GeminiNY5HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Gemini BTC-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd"],
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[GeminiNY5HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Gemini ETH-BTC",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["eth-btc"],
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[GeminiNY5HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Gemini ETH-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["eth-usd"],
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[GeminiNY5HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Gemini",
        [f"{elem:03d}" for elem in range(0, 176, 60)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.1,
                "proxy_groups": [[GeminiNY5HetznerUs.BOOK_TICK_BY_TICK, GeminiHetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
    ),
    spot_streaming_scraper(
        "Huobi Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
                "instruments": HUOBI_BUSY_SPOT_MARKETS,
            }
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Huobi K",
        [f"{elem:04d}" for elem in range(0, 700, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerEu.BOOK_TICK_BY_TICK]],
                "exclude_instruments": HUOBI_BUSY_SPOT_MARKETS,
            }
        },
    ),
    spot_streaming_scraper(
        "Huobi",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usdt",
                    "aave-usdc",
                    "aave-usdt",
                    "amp-usdt",
                    "ankr-usdt",
                    "audio-usdt",
                    "axs-usdc",
                    "axs-usdt",
                    "bat-usdt",
                    "bch-usdc",
                    "bch-usdt",
                    "bnt-usdt",
                    "btc-usdc",
                    "btc-usdt",
                    "comp-usdt",
                    "crv-usdt",
                    "doge-usdc",
                    "doge-usdt",
                    "dot-usdc",
                    "dot-usdt",
                    "enj-usdt",
                    "eth-usdc",
                    "eth-usdt",
                    "fil-usdc",
                    "fil-usdt",
                    "ftm-usdc",
                    "ftm-usdt",
                    "grt-usdt",
                    "link-usdc",
                    "link-usdt",
                    "lpt-usdt",
                    "lrc-usdc",
                    "lrc-usdt",
                    "ltc-usdc",
                    "ltc-usdt",
                    "luna-usdt",
                    "mana-usdc",
                    "mana-usdt",
                    "matic-usdc",
                    "matic-usdt",
                    "mkr-usdt",
                    "sand-usdc",
                    "sand-usdt",
                    "skl-usdt",
                    "snx-usdt",
                    "sol-usdc",
                    "sol-usdt",
                    "sushi-usdt",
                    "uni-usdt",
                    "xtz-usdc",
                    "xtz-usdt",
                    "yfi-usdt",
                    "zec-usdc",
                    "zec-usdt",
                    "zrx-usdt",
                ],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "itBit All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    spot_streaming_scraper(
        "itBit DB",
        deployment_flags={
            "entrypoint_args": {"kafka_out_quotes": ["kafka-{{ inventory_hostname }}"]},
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Kraken BatchA",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usd",
                    "aave-usd",
                    "ankr-usd",
                    "audio-usd",
                    "axs-usd",
                    "bat-usd",
                    "bch-usd",
                    "bch-usdt",
                    "bnt-usd",
                    "btc-usd",
                    "btc-usdc",
                    "btc-usdt",
                    "comp-usd",
                    "crv-usd",
                    "doge-usd",
                    "doge-usdt",
                ],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Kraken BatchB",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "dot-usd",
                    "dot-usdt",
                    "enj-usd",
                    "eth-btc",
                    "eth-usd",
                    "eth-usdc",
                    "eth-usdt",
                    "eos-usd",
                    "eos-usdt",
                    "fil-usd",
                    "ftm-usd",
                    "grt-usd",
                    "link-usd",
                    "link-usdt",
                    "lpt-usd",
                    "lrc-usd",
                ],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "Kraken BatchC",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "ltc-usd",
                    "ltc-usdt",
                    "luna-usd",
                    "mana-usd",
                    "matic-usd",
                    "matic-usdt",
                    "mkr-usd",
                    "qnt-usd",
                    "sand-usd",
                    "snx-usd",
                    "sol-usd",
                    "sushi-usd",
                    "uni-usd",
                    "xtz-usd",
                    "yfi-usd",
                    "zec-usd",
                    "zrx-usd",
                ],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Kraken All",
        [f"{elem:03d}" for elem in range(0, 892, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
            }
        },
    ),
    spot_streaming_scraper(
        "LMAX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "streaming_api_params": "{{ inventory_hostname }}:fh-books-prod-1,fh-books-prod-2",
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "MEXC Batch 1",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_1,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 2",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_2,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 3",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_3,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 4",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_4,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 5",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_5,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_3,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_3),
        "MEXC All",
        [f"{elem:04d}" for elem in range(0, 2555, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "exclude_instruments": [
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_1,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_2,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_3,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_4,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_5,
                ],
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.2,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_3),
        "OKEx All",
        [f"{elem:04d}" for elem in range(0, 712, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Poloniex All",
        [f"{elem:04d}" for elem in range(0, 995, 250)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.2,
                "markets_per_producer": 1,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
    ),
    spot_streaming_scraper(
        "Poloniex",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usdt", "eth-usdt"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "KuCoin Batch",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": ["btc-usdt", "eth-usdt", "floki-usdt"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.1,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    # *feed_handler_range_deployment_constructor(
    #     partial(spot_streaming_scraper, group=FHGroup.GROUP_1),
    #     "KuCoin",
    #     [f"{elem:04d}" for elem in range(0, 1272, 50)],
    #     deployment_flags={
    #         "entrypoint_args": {
    #             "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
    #             "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
    #             "exclude_instruments": ["btc-usdt", "eth-usdt", "floki-usdt"],
    #             "postgres_out": None,
    #             "storage_interval": None,
    #             "websocket_out": None,
    #             "book_depth": 30000,
    #             "markets_per_producer": 100,
    #             "rate_limit_multiplier": 0.15,
    #             "remove_network_exchange_sequence_id": True,
    #             "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
    #         },
    #     },
    # ),
]
FUTURES_HTTP = [
    futures_http_scraper(
        "bitFlyer",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["FX_BTC_JPY"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "rate_limit_multiplier": 0.1,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK], [SmartproxyNY5.BOOK_REALTIME]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_http_scraper(
        "Bybit",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["BTCUSD", "BTCUSDT", "ETHUSD", "ETHUSDT"],
                "rate_limit_multiplier": 0.20,
                "proxy_groups": BYBIT_PROXIES,
                "session_ttl": 600,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_http_scraper(
        "Deribit",
        deployment_flags={"entrypoint_args": {"instruments": ["BTC-.*", "ETH-.*"]}},
        group=FHGroup.GROUP_1,
    ),
    futures_http_scraper(
        "GFOX",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "prod",
                "poll_interval": 10,
                "proxy_groups": None,
                "market_source_cache_lifetime": 3600,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_http_scraper(
        "GFOX All",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "prod",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "poll_interval": 10,
                "proxy_groups": None,
                "market_source_cache_lifetime": 3600,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_http_scraper("Kraken", deployment_flags={"entrypoint_args": {"instruments": ["PI_.*"]}}, group=FHGroup.GROUP_1),
    futures_http_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["BTC-USD-SWAP", "BTC-USDT-SWAP", "ETH-USD-SWAP", "ETH-USDT-SWAP"],
                "rate_limit_multiplier": 0.2,
            },
        },
        group=FHGroup.GROUP_1,
    ),
]
FUTURES_STREAMING = [
    futures_streaming_scraper(
        "Binance DB",
        deployment_flags={
            "entrypoint_args": {
                "instruments": BINANCE_DB_FUTURE_MARKETS,
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[BinanceNY5WebShare.BOOK_REALTIME, BinanceNY5HetznerEu.BOOK_REALTIME]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *[
        futures_streaming_scraper(
            f"Binance {instrument.replace('1000', '')}",
            deployment_flags={
                "entrypoint_args": {
                    "instruments": [instrument],
                    "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                    "postgres_out": None,
                    "storage_interval": None,
                    "websocket_out": None,
                    "markets_per_producer": 1,
                    "remove_network_exchange_sequence_id": True,
                    "book_depth": 30000,
                    "rate_limit_multiplier": 0.05,
                    "proxy_groups": [[BinanceNY5WebShare.BOOK_REALTIME, BinanceNY5HetznerEu.BOOK_REALTIME]],
                },
            },
            group=FHGroup.GROUP_4,
        )
        for instrument in BINANCE_BUSY_FUTURE_MARKETS
    ],
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_4),
        "Binance K",
        [f"{elem:03d}" for elem in range(0, 495, 24)],
        deployment_flags={
            "entrypoint_args": {
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[BinanceNY5WebShare.BOOK_REALTIME, BinanceNY5HetznerEu.BOOK_REALTIME]],
                "exclude_instruments": BINANCE_BUSY_FUTURE_MARKETS,
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_3),
        "Bitfinex",
        [f"{elem:03d}" for elem in range(0, 71, 20)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
            },
        },
    ),
    futures_streaming_scraper(
        "Bitfinex",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["tBTCF0:USTF0", "tETHF0:USTF0"],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "bitFlyer All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.04,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_2,
    ),
    futures_streaming_scraper(
        "BitMEX DB",
        deployment_flags={
            "entrypoint_args": {
                "rate_limit_multiplier": 0.2,
                "instruments": ["XBTUSD", "ETHUSD"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "BitMEX K",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.05,
                "book_depth": 30000,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "Bullish DB",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": None,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_2),
        "Bybit All",
        [f"{elem:04d}" for elem in range(0, 610, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 50,
                "rate_limit_multiplier": 0.03,
                "book_depth": 30000,
                "proxy_groups": BYBIT_PROXIES,
            }
        },
    ),
    futures_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json hel1-{{ inventory_hostname }}",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.05,
                "book_depth": 30000,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    futures_streaming_scraper(
        "CME DB",
        deployment_flags={
            "entrypoint_args": {
                # e.g. BTCX1, BTCF2, ETHX1, ETHZ1
                "instruments": ["BTC[A-Z][0-9]", "ETH[A-Z][0-9]", "MBT[A-Z][0-9]", "MET[A-Z][0-9]"],
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json {{ inventory_hostname }}",
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "CoinbaseDer",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.25,
                "book_depth": 30000,
                "streaming_api_params": "{{ inventory_hostname }}:fh-books-prod-1,fh-books-prod-2",
                "api_params": "{{ inventory_hostname }}:fh-books-prod-1,fh-books-prod-2",
                "disable_compute_limited_delta": True,
                "market_source_cache_lifetime": 1800,
                "compress_books_before_send": True,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "CoinbaseInt",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[CoinbaseIntNY5HetznerEu.BOOK_TICK_BY_TICK, CoinbaseIntProdHetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "CoinbaseInt DB",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": None,
                "proxy_groups": [[CoinbaseIntNY5HetznerEu.BOOK_TICK_BY_TICK, CoinbaseIntProdHetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_1),
        "Crypto.com All",
        [f"{elem:03d}" for elem in range(0, 204, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
            }
        },
    ),
    futures_streaming_scraper(
        "Deribit All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_1),
        "dYdX",
        [f"{elem:03d}" for elem in range(0, 234, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
            },
        },
    ),
    futures_streaming_scraper(
        "dYdX DB",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["BTC-USD", "ETH-USD"],
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "ErisX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
                "compress_books_before_send": True,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "ErisX DB",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerEu.BOOK_TICK_BY_TICK, SmartproxyNY5.BOOK_REALTIME]],
                "compress_books_before_send": True,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_2),
        "Gate.io All",
        [f"{elem:04d}" for elem in range(0, 597, 120)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "remove_network_exchange_sequence_id": True,
            }
        },
    ),
    futures_streaming_scraper(
        "Huobi Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
                "instruments": HUOBI_BUSY_FUTURE_MARKETS,
            }
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_4),
        "Huobi K",
        [f"{elem:03d}" for elem in range(0, 174, 12)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
                "exclude_instruments": HUOBI_BUSY_FUTURE_MARKETS,
            }
        },
    ),
    futures_streaming_scraper(
        "Huobi BTC",
        deployment_flags={"entrypoint_args": {"instruments": ["BTC-USDT*_SWAP"]}},
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "Huobi ETH",
        deployment_flags={"entrypoint_args": {"instruments": ["ETH-USDT*_SWAP"]}},
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "Kraken Batch",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": KRAKEN_BUSY_FUTURE_MARKETS,
            }
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_4),
        "Kraken All",
        [f"{elem:03d}" for elem in range(0, 332, 100)],
        deployment_flags={
            "entrypoint_args": {
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "exclude_instruments": KRAKEN_BUSY_FUTURE_MARKETS,
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_3),
        "MEXC All",
        [f"{elem:04d}" for elem in range(0, 766, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME, HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_3),
        "OKEx All",
        [f"{elem:03d}" for elem in range(0, 267, 40)],
        deployment_flags={
            "entrypoint_args": {
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
    ),
    futures_streaming_scraper(
        "KuCoin DB",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": None,
                "instruments": ["BCHUSDTM", "ETHUSDTM"],
                "rate_limit_multiplier": 0.04,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 1",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_1,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.03,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 2",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_2,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.03,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 3",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_3,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.03,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 4",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_4,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.03,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 5",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_5,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.03,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_1),
        "KuCoin",
        [f"{elem:04d}" for elem in range(0, 370, 15)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "exclude_instruments": [
                    *KUCOIN_BUSY_FUTURE_MARKETS_1,
                    *KUCOIN_BUSY_FUTURE_MARKETS_2,
                    *KUCOIN_BUSY_FUTURE_MARKETS_3,
                    *KUCOIN_BUSY_FUTURE_MARKETS_4,
                    *KUCOIN_BUSY_FUTURE_MARKETS_5,
                ],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.15,
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
    ),
]

OPTION_HTTP = [
    option_http_scraper(
        "GFOX All",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "prod",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "poll_interval": 10,
                "proxy_groups": None,
                "market_source_cache_lifetime": 3600,
            },
        },
        group=FHGroup.GROUP_1,
    ),
]

OPTION_STREAMING = [
    *feed_handler_range_deployment_constructor(
        partial(option_streaming_scraper, group=FHGroup.GROUP_1),
        "Binance",
        [f"{elem:03d}" for elem in range(0, 1100, 700)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 100,
                "remove_network_exchange_sequence_id": True,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[BinanceNY5HetznerEu.BOOK_REALTIME, BinanceNY5WebShare.BOOK_REALTIME]],
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(option_streaming_scraper, group=FHGroup.GROUP_1),
        "Binance D",
        [f"{elem:03d}" for elem in range(0, 1100, 700)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": None,
                "markets_per_producer": 100,
                "remove_network_exchange_sequence_id": True,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[BinanceNY5HetznerEu.BOOK_REALTIME, BinanceNY5WebShare.BOOK_REALTIME]],
            }
        },
    ),
    option_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json {{ inventory_hostname }}",
                "proxy_groups": [[HetznerUs.BOOK_TICK_BY_TICK, HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
        group=FHGroup.GROUP_1,
    ),
    option_streaming_scraper(
        "CME DB",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": None,
                "proxy_groups": [[HetznerUs.BOOK_REALTIME]],
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json {{ inventory_hostname }}",
            }
        },
        group=FHGroup.GROUP_1,
    ),
    option_streaming_scraper(
        "Deribit",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 500,
                "book_depth": 30000,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    option_streaming_scraper(
        "Deribit DB",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": None,
                "markets_per_producer": 500,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    option_streaming_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 40,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    option_streaming_scraper(
        "OKEx DB",
        deployment_flags={"entrypoint_args": {"kafka_out_proto": None, "markets_per_producer": 40}},
        group=FHGroup.GROUP_1,
    ),
]

REALTIME_BOOK: List[Scraper] = [*SPOT_HTTP, *SPOT_STREAMING, *FUTURES_HTTP, *FUTURES_STREAMING, *OPTION_HTTP, *OPTION_STREAMING]
