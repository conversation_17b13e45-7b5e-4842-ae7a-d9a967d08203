import gzip
import json
from calendar import monthrange
from datetime import date, datetime, timedelta
from decimal import Decimal
from os.path import basename, dirname, join
from typing import Any, Dict, Iterable, List, Optional, Set

import ciso8601
import pytz

from src.octopus.data import (
    BookData,
    BookType,
    FuturesContractData,
    HistoryMarker,
    Instrument,
    Market,
    OptionContractData,
    OptionType,
    PriceLevel,
    Trade,
    TradeData,
)
from src.octopus.entry_history import TraversalBase, TraversalResult
from src.octopus.exchange.api import (
    BookStreamParams,
    ExchangeMarkets,
    IClientFactory,
    IHistoryTraversal,
    Instruments,
    OpenInterestData,
    TradeCallback,
    TradeStreamParams,
    TraversalMethod,
)
from src.octopus.exchange.api_utils import ExchangeHttpApiBase, ExchangeStreamingApiBase
from src.octopus.stream_components.book import BookComponent
from src.octopus.stream_components.subscription import FixedScrapedMarketCountComponent
from src.octopus.translation import ExchangeTickerTranslator
from src.octopus.utils import compress_price_levels
from src.resources.exchange import glib_exchange
from src.utils.aws import AwsS3Client
from src.utils.diagnostics import Diagnostics
from src.utils.execution import IRunnable
from src.utils.http import IHttpClient, get_json
from src.utils.simple_binary_encoding import DecodedMessage
from src.utils.stream import CmeSmartStreamFixTranslator, ComponentProcessor, IStreamApi, IStreamProcessor, Stream
from src.utils.timeutil import ProvideCurrentTime, dt_from_any_aware, dt_from_us, dt_from_us_aware, dt_to_us
from src.utils.types import JsonValue

EXCHANGE_NAME = "CME"

MAX_TRADES_PER_REQUEST = 1000
FUTURES_SYMBOLS = ["BTC", "ETH", "MBT", "MET", "BFF", "SOL", "MSL"]
OPTIONS_SYMBOLS = ["BTC", "ETH", "WM", "VM"]
AWS_S3_BUCKET_CME = "cm-cme-historical-production"

MARKET_TZ = pytz.timezone("America/Chicago")
MARKET_CLOSE_HOUR = 16  # 4pm
MARKET_OPEN_HOUR = 17  # 5pm
TRADE_GRACE_PERIOD = timedelta(hours=2)  # allow trades timestamped within this period of open/close for safety
PUBLISH_GRACE_PERIOD = timedelta(hours=2)  # allow trades published within this period of open/close for safety

# Special holidays that fall on the last Friday of the month (US or UK) from 2017 through 2050
SPECIAL_HOLIDAYS = {
    date(2018, 3, 30),
    date(2020, 12, 25),
    date(2024, 3, 29),
    date(2025, 12, 26),
    date(2026, 12, 25),
    date(2027, 3, 26),
    date(2029, 3, 30),
    date(2031, 12, 26),
    date(2032, 3, 26),
    date(2036, 12, 26),
    date(2037, 12, 25),
    date(2040, 3, 30),
    date(2042, 12, 26),
    date(2043, 3, 27),
    date(2043, 12, 25),
    date(2048, 12, 25),
}


class CMEMismatchTransactTimeException(Exception):
    def __init__(self, err_msg):
        self.message = f"Mismatch transact_time: {err_msg}"


class CMEMismatchLastSeqNumException(Exception):
    def __init__(self, err_msg):
        self.message = f"Mismatch last_seq_num: {err_msg}"


def _extract_display_factor(product_info: Dict[str, Any]) -> int:
    """CME documentation for globexDisplayFactor here: CME Reference Data API Version 2
    https://www.cmegroup.com/confluence/pages/viewpage.action?pageId=152797362
    """
    return int(10 ** int(product_info["globexDisplayFactor"][0]))


class CmeHttpApi(ExchangeHttpApiBase):
    _PRODUCT_CODE_URL = "https://refdata.api.cmegroup.com/refdata/v3/products?globexProductCode={product_code}"
    _SECURITY_TYPE_FUTURES = {"COMBO", "FUT"}
    _SECURITY_TYPE_OPTIONS = {"OOF", "OOC"}
    _USER_AGENT_HEADERS = {"User-Agent": "Mozilla/Firefox"}

    def __init__(
        self,
        exchange_name: str,
        ticker_translator: ExchangeTickerTranslator,
        diagnostics: Diagnostics,
        client_id: str = "",
        client_secret: str = "",
        aws_client_id: str = "",
        aws_secret: str = "",
        current_time_provider: ProvideCurrentTime = lambda: datetime.utcnow(),
    ):
        super().__init__(exchange_name, ticker_translator, diagnostics)
        self._client_id = client_id
        self._client_secret = client_secret
        self._current_time_provider = current_time_provider
        self._cme_proxy = CmeS3Proxy(AWS_S3_BUCKET_CME, aws_client_id, aws_secret, diagnostics)
        # local caches for performance/cost reduction
        self._oauth2_cache: JsonValue = {}
        self._secdef_cache: JsonValue = {}
        self._last_trade_cache: Dict[str, TradeData] = {}
        self._last_open_interest_cache: Dict[str, OpenInterestData] = {}

    def futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        self._secdef_cache = self._cme_proxy.get_latest_secdef(self._secdef_cache)
        return ExchangeMarkets(self._get_active_futures_instruments(client))

    def option_markets(self, client: IHttpClient) -> ExchangeMarkets:
        return ExchangeMarkets(self._get_active_options_instruments(client))

    def futures_markets_metadata(self, client: IHttpClient) -> List[FuturesContractData]:
        self._secdef_cache = self._cme_proxy.get_latest_secdef(self._secdef_cache)

        markets = self._extract_instruments_from_secdef_cache()
        for instrument in self._get_active_futures_instruments(client):
            markets[instrument.symbol] = instrument

        result: List[FuturesContractData] = []
        for symbol, instrument in markets.items():
            base = instrument.metadata["base"]
            quote = instrument.metadata["quote"]
            margin_asset = instrument.metadata["margin_asset"]

            if base.lower() == "mbt":
                base_id = self._translator.to_cm_id("btc")
            elif base.lower() == "met":
                base_id = self._translator.to_cm_id("eth")
            else:
                base_id = self._translator.to_cm_id(base)
            contract_data = FuturesContractData(
                symbol=symbol,
                underlying_base_id=base_id,
                underlying_quote_id=self._translator.to_cm_id(quote),
                size_asset_id=base_id,
                margin_asset_id=self._translator.to_cm_id(margin_asset),
                underlying_base_name=self.translator.translate(base),
                underlying_quote_name=self.translator.translate(quote),
                size_asset_name=self.translator.translate(base),
                margin_asset_name=self.translator.translate(margin_asset),
                underlying_native_base_name=instrument.metadata.get("native_base"),
                underlying_native_quote_name=instrument.metadata.get("native_quote"),
                listing_date=instrument.metadata["listing_date"],
                expiry_date=instrument.metadata["expiry_date"],
                contract_size=instrument.metadata["contract_size"],
                tick_size=instrument.metadata["tick_size"],
                amount_size_min=instrument.metadata.get("amount_size_min"),
                amount_size_max=instrument.metadata.get("amount_size_max"),
                price_size_min=instrument.metadata.get("price_size_min"),
                price_increment=instrument.metadata.get("tick_size"),
            )
            result.append(contract_data)
        return result

    def option_markets_metadata(self, client: IHttpClient) -> List[OptionContractData]:
        result: set[OptionContractData] = set()  # There are duplicates
        for market_info in self._get_options_markets_info(client):
            base = market_info[0]["globexProductCode"].lower()
            quote = market_info[0]["tradePxCcy"].lower()
            size_asset = base
            margin_asset = market_info[0]["settlePxCcy"].lower()
            vtt_low_tick = Decimal(
                market_info[0].get("vttLowTick") or market_info[1].get("vttLowTick") or market_info[0]["pxUnitOfMeasureQty"]
            )
            if base.lower() == "wm":
                base_id = self._translator.to_cm_id("btc")
            elif base.lower() == "vm":
                base_id = self._translator.to_cm_id("eth")
            else:
                base_id = self._translator.to_cm_id(base)
            contract_data = OptionContractData(
                symbol=self._fix_option_symbol_name(market_info[1]["globexSymbol"]),
                underlying_base_id=base_id,
                underlying_quote_id=self.translator.to_cm_id(quote),
                size_asset_id=base_id,
                margin_asset_id=self.translator.to_cm_id(margin_asset),
                underlying_base_name=self.translator.translate(base),
                underlying_quote_name=self.translator.translate(quote),
                underlying_native_base_name=market_info[0]["globexProductCode"],
                underlying_native_quote_name=market_info[0]["tradePxCcy"],
                size_asset_name=self.translator.translate(size_asset),
                margin_asset_name=self.translator.translate(margin_asset),
                option_type=OptionType.CALL if market_info[1]["putCallIndicator"] == "C" else OptionType.PUT,
                strike=Decimal(market_info[1]["strikePx"]),
                expiry_date=_chicago_to_utc_dt(datetime.strptime(market_info[1]["globexLastTradeDate"], "%Y%m%d%H%M%S")),
                size=Decimal(market_info[0]["contractNotionalAmount"]),
                is_european=market_info[0]["exerciseStyleAmericanEuropean"] == "1",
                listing_date=_chicago_to_utc_dt(datetime.strptime(market_info[1]["globexFirstTradeDate"], "%Y%m%d%H%M%S")),
                tick_size=vtt_low_tick,
                amount_size_min=Decimal(market_info[0]["minGlobexOrdQty"]),
                amount_size_max=Decimal(market_info[0]["maxGlobexOrdQty"]),
                price_increment=vtt_low_tick,
                price_size_min=self._convert_19_character_cme_format_string_to_decimal(market_info[0]["globexCabPx"]),
            )
            result.add(contract_data)
        return list(result)

    @staticmethod
    def _convert_19_character_cme_format_string_to_decimal(string_value: str) -> Decimal:
        first_char = int(string_value[0])
        return Decimal(string_value[1:]) // 10**first_char

    def historical_futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        self._secdef_cache = self._cme_proxy.get_latest_secdef(self._secdef_cache)
        markets = self._extract_instruments_from_secdef_cache()
        for instrument in self._get_active_futures_instruments(client):
            markets[instrument.symbol] = instrument
        self.diagnostics.debug(f"historical_futures_markets: Returning {len(markets)} instruments")
        return ExchangeMarkets(list(markets.values()))

    # Note: the published DisplayFactor is handled as (1/DisplayFactor) internally (see _extract_display_factor)
    def _extract_instruments_from_secdef_cache(self) -> Dict[str, Instrument]:
        results: Dict[str, Instrument] = {}
        for symbol, message in self._secdef_cache["assets"].items():
            base = message["Asset"].lower()
            results[symbol] = Instrument.futures(
                symbol=message["Symbol"],
                metadata={
                    "security_id": message["SecurityID"],
                    "display_factor": int(Decimal("1") / Decimal(message["DisplayFactor"])),
                    "base": message["Asset"].lower(),
                    "quote": message["Currency"].lower(),
                    "margin_asset": message["Currency"].lower(),
                    "listing_date": ciso8601.parse_datetime_as_naive(message["TradingReferenceDate"]),
                    "expiry_date": _cme_maturity_to_dt(message["MaturityMonthYear"]),
                    "contract_size": Decimal(message.get("UnitOfMeasureQty", 5 if base == "btc" else 50)),
                    "tick_size": Decimal(message["MinPriceIncrement"]) * Decimal(message["DisplayFactor"]),
                },
            )
        return results

    def _get_active_futures_instruments(self, client: IHttpClient) -> List[Instrument]:
        result: List[Instrument] = []
        for product, market_info in self._get_markets_info(
            client,
            symbols=FUTURES_SYMBOLS,
            security_type=self._SECURITY_TYPE_FUTURES,
            base_url=self._PRODUCT_CODE_URL,
            active_only=True,
        ):
            if market_info["globexSecurityId"] is None:
                continue

            # The CME publishes globex date/time expressed in US/Central, so it needs to be converted to UTC
            listing_date = _chicago_to_utc_dt(datetime.strptime(market_info["globexFirstTradeDate"], "%Y%m%d%H%M%S"))
            expiry_date = _chicago_to_utc_dt(datetime.strptime(market_info["globexLastTradeDate"], "%Y%m%d%H%M%S"))

            result.append(
                Instrument.futures(
                    symbol=market_info["globexSymbol"],
                    metadata={
                        "security_id": int(market_info["globexSecurityId"]),
                        "display_factor": _extract_display_factor(product),
                        "base": product["globexProductCode"].lower(),
                        "quote": product["tradePxCcy"].lower(),
                        "native_base": product["globexProductCode"],
                        "native_quote": product["tradePxCcy"],
                        "margin_asset": product["settlePxCcy"].lower(),
                        "listing_date": listing_date,
                        "expiry_date": expiry_date,
                        "contract_size": Decimal(product["contractNotionalAmount"]),
                        "tick_size": Decimal(market_info["tradeTick"]) if market_info.get("tradeTick") else Decimal("5"),
                        "amount_size_min": (Decimal(market_info["minGlobexOrdQty"]) if market_info["minGlobexOrdQty"] else None),
                        "amount_size_max": (Decimal(market_info["maxGlobexOrdQty"]) if market_info["maxGlobexOrdQty"] else None),
                        "price_size_min": Decimal(product["globexCabPx"]),
                    },
                )
            )
        return result

    def _get_active_options_instruments(self, client: IHttpClient) -> List[Instrument]:
        result: List[Instrument] = []
        for product, market_info in self._get_markets_info(
            client,
            symbols=OPTIONS_SYMBOLS,
            security_type=self._SECURITY_TYPE_OPTIONS,
            base_url=self._PRODUCT_CODE_URL,
            active_only=True,
        ):
            if market_info["globexSecurityId"] is None:
                continue

            # The CME publishes globex date/time expressed in US/Central, so it needs to be converted to UTC
            listing_date = _chicago_to_utc_dt(datetime.strptime(market_info["globexFirstTradeDate"], "%Y%m%d%H%M%S"))
            expiry_date = _chicago_to_utc_dt(datetime.strptime(market_info["globexLastTradeDate"], "%Y%m%d%H%M%S"))

            result.append(
                Instrument.option(
                    symbol=self._fix_option_symbol_name(market_info["globexSymbol"]),
                    metadata={
                        "security_id": int(market_info["globexSecurityId"]),
                        "display_factor": _extract_display_factor(product),
                        "base": product["globexProductCode"].lower(),
                        "quote": product["tradePxCcy"].lower(),
                        "margin_asset": product["settlePxCcy"].lower(),
                        "listing_date": listing_date,
                        "expiry_date": expiry_date,
                        "contract_size": Decimal(product["contractNotionalAmount"]),
                        "tick_size": Decimal(market_info["tradeTick"]) if market_info.get("tradeTick") else Decimal("5"),
                    },
                )
            )
        return result

    def get_oauth2_header(self, client: IHttpClient) -> JsonValue:
        if "expires" not in self._oauth2_cache or datetime.now() > self._oauth2_cache["expires"]:
            data = {"grant_type": "client_credentials", "client_id": self._client_id, "client_secret": self._client_secret}
            auth_response = get_json(client.post("https://auth.cmegroup.com/as/token.oauth2", data=data))
            self._oauth2_cache["auth"] = {"Authorization": f"{auth_response['token_type']} {auth_response['access_token']}"}
            # Give ourselves a 60 second buffer to ensure the auth doesn't expire in-flight
            self._oauth2_cache["expires"] = datetime.now() + timedelta(seconds=auth_response["expires_in"] - 60)
        return self._oauth2_cache["auth"]

    def last_trades(self, client: IHttpClient, instrument: Instrument) -> List[TradeData]:
        if instrument.symbol not in self._last_trade_cache:
            trade_events = self._cme_proxy.get_last_trade_message(instrument.symbol)
            display_factor = instrument.metadata["display_factor"]
            last_trade = list(_trades_from_fix(trade_events, display_factor))[0] if trade_events is not None else None
        else:
            cached_trade = self._last_trade_cache[instrument.symbol]
            trade_events = self.trades_since(instrument, HistoryMarker(0, cached_trade.time))
            last_trade = trade_events[-1] if trade_events else cached_trade
        if last_trade is None:
            return []

        self._last_trade_cache[instrument.symbol] = last_trade
        return [last_trade]

    @staticmethod
    def trade_history_traversal_method() -> TraversalMethod:
        return TraversalMethod.TIME

    def trade_history_traversal(self, client: IHttpClient, instrument: Instrument) -> IHistoryTraversal[TradeData]:
        return _TradeHistoryTraversal(instrument, client, self)

    def trades_since(self, instrument: Instrument, marker: Optional[HistoryMarker]) -> List[TradeData]:
        if not (trades_list := self._cme_proxy.get_file_keys(instrument.symbol, "trades")):
            return []
        next_key = trades_list[0] if marker is None else self._cme_proxy.get_next_trade_key(trades_list, marker.time)
        if next_key is None:
            return []

        trades: List[TradeData] = []
        for trade_message in json.loads(self._cme_proxy.get_contents(next_key))["trades"]:
            trades.extend(_trades_from_fix(trade_message, instrument.metadata["display_factor"]))
        return trades

    def last_open_interest(self, client: IHttpClient, instrument: Instrument) -> List[OpenInterestData]:
        # open interest not supported for spread contracts
        if "-" in instrument.symbol:
            return []
        if instrument.symbol not in self._last_open_interest_cache:
            oi_events = self._cme_proxy.get_last_open_interest_message(instrument.symbol)
            last_oi = _open_interest_from_fix(oi_events) if oi_events is not None else None
        else:
            cached_oi = self._last_open_interest_cache[instrument.symbol]
            oi_events = self.open_interest_since(instrument, HistoryMarker(0, cached_oi.time))
            last_oi = oi_events[-1] if oi_events else cached_oi
        if last_oi is None:
            return []

        self._last_open_interest_cache[instrument.symbol] = last_oi
        return [last_oi]

    def open_interest_history_traversal_method(self) -> TraversalMethod:
        return TraversalMethod.TIME

    def open_interest_history_traversal(
        self, client: IHttpClient, instrument: Instrument
    ) -> IHistoryTraversal[OpenInterestData]:
        return _OpenInterestHistoryTraversal(instrument, client, self)

    def open_interest_since(self, instrument: Instrument, marker: Optional[HistoryMarker]) -> List[OpenInterestData]:
        # open interest not supported for spread contracts
        if "-" in instrument.symbol or not (oi_list := self._cme_proxy.get_file_keys(instrument.symbol, "open_interest")):
            return []
        next_key = oi_list[0] if marker is None else self._cme_proxy.get_next_open_interest_key(oi_list, marker.time)
        if next_key is None:
            return []
        return [_open_interest_from_fix(json.loads(self._cme_proxy.get_contents(next_key)))]

    def _get_markets_info(
        self,
        client: IHttpClient,
        symbols: List[str],
        security_type: Set[str],
        base_url: str,
        active_only: bool = True,
    ) -> Iterable[JsonValue]:
        headers = {**self.get_oauth2_header(client), **self._USER_AGENT_HEADERS}
        for product_code in symbols:
            product_url = base_url.format(product_code=product_code)
            while product_url is not None:
                product_data = get_json(client.get(product_url, headers=headers, timeout=30))
                if "_embedded" in product_data:
                    for product in product_data["_embedded"]["products"]:
                        if product["securityType"] not in security_type:
                            continue

                        instrument_url = product["_links"]["instruments"]["href"]
                        while instrument_url is not None:
                            instruments_data = get_json(client.get(instrument_url, headers=headers, timeout=30))
                            if "_embedded" in instruments_data:
                                for inst in instruments_data["_embedded"]["instruments"]:
                                    if active_only:
                                        if (
                                            inst.get("globexSecurityId") is not None
                                            and inst.get("lastTradeDate") is not None
                                            and datetime.now(tz=MARKET_TZ) <= dt_from_any_aware(inst["lastTradeDate"])
                                        ):
                                            yield product, inst
                                    else:
                                        if inst.get("globexSecurityId") is not None:
                                            yield product, inst

                            instrument_url = instruments_data["_links"].get("next", {}).get("href")
                product_url = product_data["_links"].get("next", {}).get("href")

    def _get_options_markets_info(self, client: IHttpClient) -> Iterable[JsonValue]:
        return self._get_markets_info(
            client,
            symbols=OPTIONS_SYMBOLS,
            security_type=self._SECURITY_TYPE_OPTIONS,
            base_url=self._PRODUCT_CODE_URL,
            active_only=False,
        )

    def option_settle_price(self, client: IHttpClient, symbol: str) -> Optional[float]:
        # We don't need to populate settlement price:
        # https://coinmetrics.slack.com/archives/C01V97CKSUQ/p1694029710221279?thread_ts=1692627319.303359&cid=C01V97CKSUQ
        return None

    @staticmethod
    def _fix_option_symbol_name(symbol_name: str) -> str:
        return symbol_name.replace(" ", "-")


def _cme_time_to_ns(cme_time: str) -> str:
    cme_us = dt_to_us(ciso8601.parse_datetime_as_naive(f"{cme_time[:8]}T{cme_time[8:14]}.{cme_time[14:]}Z"))
    return f"{int(cme_us)}{cme_time[-3:]}"


def _trades_from_fix(message: JsonValue, display_factor: int) -> Iterable[TradeData]:
    for trade in message["NoMDEntries"]:
        cme_trade_time = _cme_time_to_ns(message["TransactTime"])
        yield TradeData(
            int(cme_trade_time + trade["MDTradeEntryID"]),
            Decimal(trade["MDEntrySize"]),
            Decimal(trade["MDEntryPx"]) / display_factor,
            None if trade["AggressorSide"] == "0" else trade["AggressorSide"] == "1",
            dt_from_us(int(cme_trade_time) // 1000),
        )


def _open_interest_from_fix(message: JsonValue) -> Optional[OpenInterestData]:
    quantity = Decimal(message["OpenInterestQty"])
    if "TradingReferencePrice" in message and "DisplayFactor" in message:
        single_asset_price = Decimal(message["TradingReferencePrice"]) * Decimal(message["DisplayFactor"])
        asset_price = quantity * single_asset_price * Decimal(message["UnitOfMeasureQty"])
    else:
        asset_price = Decimal("-1")  # price is not defined

    return OpenInterestData(quantity, asset_price, _get_cme_market_close_utc(message["yyyymmdd"]))


def _get_cme_market_close_utc(date_string: str) -> datetime:
    return _chicago_to_utc_dt(ciso8601.parse_datetime_as_naive(f"{date_string}T{MARKET_CLOSE_HOUR}0000"))


def _chicago_to_utc_dt(naive: datetime) -> datetime:
    chicago_timestamp: datetime = MARKET_TZ.localize(naive)
    return chicago_timestamp.astimezone(pytz.timezone("UTC")).replace(tzinfo=None)


# Special rules for converting the YYYYMM maturity date into an actual calendar day, using last Friday of the month
# rule, unless that day is a "special" holiday (see above), in which case, use the previous day.
def _cme_maturity_to_dt(cme_maturity: str) -> datetime:
    if len(cme_maturity) != 6:
        raise ValueError(f"Invalid maturity: {cme_maturity} -- must be YYYYMM")
    year = int(cme_maturity[:4])
    month = int(cme_maturity[-2:])
    month_end = datetime(year, month, monthrange(year, month)[1], 0, 0)
    maturity = month_end - timedelta(days=(month_end.weekday() + 3) % 7)
    if maturity.date() in SPECIAL_HOLIDAYS:
        maturity -= timedelta(days=1)
    return maturity


class _TradeHistoryTraversal(TraversalBase[CmeHttpApi, TradeData]):
    def first_entries(self) -> TraversalResult[TradeData]:
        return self._get_next_trade(None)

    def next_entries(self, reference: HistoryMarker) -> TraversalResult[TradeData]:
        return self._get_next_trade(reference)

    def _get_next_trade(self, reference: Optional[HistoryMarker]) -> TraversalResult[TradeData]:
        if not (trades := self._api.trades_since(self._instrument, marker=reference)):
            return TraversalResult([], last_entry_marker=reference)
        marker = HistoryMarker(id=trades[-1].trade_id, time=trades[-1].time)
        return TraversalResult(trades, last_entry_marker=marker)


class _OpenInterestHistoryTraversal(TraversalBase[CmeHttpApi, OpenInterestData]):
    def first_entries(self) -> TraversalResult[OpenInterestData]:
        return self._get_next_open_interest(None)

    def next_entries(self, reference: HistoryMarker) -> TraversalResult[OpenInterestData]:
        return self._get_next_open_interest(reference)

    def _get_next_open_interest(self, reference: Optional[HistoryMarker]) -> TraversalResult[OpenInterestData]:
        oi = self._api.open_interest_since(self._instrument, marker=reference)
        if not oi:
            return TraversalResult([], last_entry_marker=reference)
        return TraversalResult(oi, last_entry_marker=HistoryMarker(id=0, time=oi[-1].time))


class CmeS3Proxy:
    def __init__(self, bucket: str, client_id: str, secret_id: str, diagnostics: Diagnostics):
        self._s3client = AwsS3Client(bucket, client_id, secret_id, timedelta(minutes=12), diagnostics)
        self._diagnostics = diagnostics

    def get_file_keys(self, symbol: str, data_type: str) -> List[str]:
        # The s3 client caches the results, so it's faster/cheaper to pull the entire tree in a single list request
        all_keys = self._s3client.list_keys(f"v3/{data_type}/")
        return [file for file in all_keys if file.startswith(f"v3/{data_type}/{symbol}/")]

    def get_contents(self, key: str) -> bytes:
        contents = self._s3client.get(key)
        return gzip.decompress(contents) if key.endswith(".gz") else contents

    def get_last_trade_message(self, symbol: str) -> Optional[JsonValue]:
        trade_keys = self.get_file_keys(symbol, "trades")
        if not trade_keys:
            return None
        trade_data = json.loads(self.get_contents(trade_keys[-1]))
        return trade_data["trades"][-1]

    def get_next_trade_key(self, keys: List[str], marker: datetime) -> Optional[str]:
        new_keys = [key for key in keys if marker < self._get_date_from_key(key, last=True)]
        return new_keys[0] if new_keys else None

    @staticmethod
    def get_next_open_interest_key(keys: List[str], marker: datetime) -> Optional[str]:
        new_keys = [key for key in keys if marker < _get_cme_market_close_utc(basename(key)[:8])]
        return new_keys[0] if new_keys else None

    def get_last_open_interest_message(self, symbol: str) -> Optional[JsonValue]:
        open_interest_keys = self.get_file_keys(symbol, "open_interest")
        if not open_interest_keys:
            return None
        return json.loads(self.get_contents(open_interest_keys[-1]))

    @staticmethod
    def _get_date_from_key(key: str, last: bool = False) -> datetime:
        trade_times = basename(key).split(".")[0].split("-")
        cme_time = trade_times[1 if last else 0]
        return ciso8601.parse_datetime_as_naive(f"{cme_time[:8]}T{cme_time[8:14]}.{cme_time[14:]}Z")

    def get_latest_secdef(self, secdef: JsonValue) -> JsonValue:
        secdef_key = "v3/secdef.json"
        proxy_last_modified = self._s3client.get_last_modified(secdef_key)
        if secdef.get("as_of", None) == proxy_last_modified:
            return secdef
        updated_secdef = json.loads(self.get_contents(secdef_key))
        updated_secdef["as_of"] = proxy_last_modified
        self._diagnostics.info(f"Loading secdef from: {secdef_key} ({proxy_last_modified})")
        return updated_secdef


class CmeWebSocketApi(ExchangeStreamingApiBase):
    _CME_PROJECT_NAME = "cmegroup-marketdata-cr"
    _CME_FUTURES_TRADES_TOPIC = "PROD.SSCL.GCP.MD.RT.CMEG.FIXBIN.v01000.INCR.326"
    _CME_OPTIONS_TRADES_TOPIC = "PROD.SSCL.GCP.MD.RT.CMEG.FIXBIN.v01000.INCR.327"
    _CME_FUTURES_BOOKS_TOPIC = "PROD.SSCL.GCP.MD.RT.CMEG.FIXBIN.v01000.MBORPLY.326"
    _CME_OPTIONS_BOOKS_TOPIC = "PROD.SSCL.GCP.MD.RT.CMEG.FIXBIN.v01000.MBORPLY.327"

    def __init__(
        self,
        exchange_name: str,
        ticker_translator: ExchangeTickerTranslator,
        http_client: IHttpClient,
        client_json_file_path: str = "coinmetrics-f5d38728cc2a.json",
        hostname: str = "",
        topic_name: Optional[str] = None,
    ) -> None:
        super().__init__(exchange_name, ticker_translator, http_client)

        with open(join(dirname(__file__), "resources", "cme", client_json_file_path)) as client_json_file:
            self._client_json = client_json_file.read()
        self._hostname = self.strip_hostname(hostname)
        self._template_path = join(dirname(__file__), "resources", "cme", "template_FixBinary.xml")
        self._topic_name = topic_name

    def futures_trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        message_name = "MDIncrementalRefreshTradeSummary48"
        processor: ComponentProcessor[Optional[DecodedMessage]] = ComponentProcessor(
            FixedScrapedMarketCountComponent(gauge=params.scraped_market_count, value=len(instruments)),
            _CmeMarketHoursComponent("futures_trades"),
            _TradeComponent(instruments, params.on_trades),
            break_after_first_message_recognition=True,
        )

        topic_name = self._topic_name or self._CME_FUTURES_TRADES_TOPIC
        subscription_topic = f"projects/{self._CME_PROJECT_NAME}/topics/{topic_name}"
        params.diagnostics.info(f"Creating Google pub/sub client: {self._hostname}: {subscription_topic}")
        client = factory.google_pub_sub(
            self._client_json, subscription_topic, f"trade-binary-{topic_name.split('.')[-1]}", self._hostname
        )
        translator = CmeSmartStreamFixTranslator(self._template_path, message_name)
        return Stream(client, processor, translator, params.diagnostics)

    def option_trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        message_name = "MDIncrementalRefreshTradeSummary48"
        processor: ComponentProcessor[Optional[DecodedMessage]] = ComponentProcessor(
            FixedScrapedMarketCountComponent(gauge=params.scraped_market_count, value=len(instruments)),
            _CmeMarketHoursComponent("option_trades"),
            _TradeComponent(instruments, params.on_trades),
            break_after_first_message_recognition=True,
        )

        subscription_topic = f"projects/{self._CME_PROJECT_NAME}/topics/{self._CME_OPTIONS_TRADES_TOPIC}"
        params.diagnostics.info(f"Creating Google pub/sub client: {self._hostname}: {subscription_topic}")
        client = factory.google_pub_sub(
            self._client_json,
            subscription_topic,
            f"trade-binary-{self._CME_OPTIONS_TRADES_TOPIC.split('.')[-1]}",
            self._hostname,
        )
        translator = CmeSmartStreamFixTranslator(self._template_path, message_name)
        return Stream(client, processor, translator, params.diagnostics)

    def futures_books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        message_name = "SnapshotFullRefreshOrderBook53"
        processor: ComponentProcessor[Optional[DecodedMessage]] = ComponentProcessor(
            FixedScrapedMarketCountComponent(gauge=params.scraped_market_count, value=len(instruments)),
            _CmeMarketHoursComponent("futures_books"),
            _BookComponent(instruments, params),
            break_after_first_message_recognition=True,
        )

        topic_name = self._topic_name or self._CME_FUTURES_BOOKS_TOPIC
        subscription_topic = f"projects/{self._CME_PROJECT_NAME}/topics/{topic_name}"
        client_name = f"book-binary-{topic_name.split('.')[-1]}"
        if params.poll_max_book:
            client_name = f"full-book-binary-{topic_name.split('.')[-1]}"
        params.diagnostics.info(f"Creating Google pub/sub client: {self._hostname}: {subscription_topic}")
        client = factory.google_pub_sub(self._client_json, subscription_topic, client_name, self._hostname)
        translator = CmeSmartStreamFixTranslator(self._template_path, message_name)
        return Stream(client, processor, translator, params.diagnostics)

    def option_books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        message_name = "SnapshotFullRefreshOrderBook53"
        processor: ComponentProcessor[Optional[DecodedMessage]] = ComponentProcessor(
            FixedScrapedMarketCountComponent(gauge=params.scraped_market_count, value=len(instruments)),
            _CmeMarketHoursComponent("option_books"),
            _BookComponent(instruments, params),
            break_after_first_message_recognition=True,
        )

        topic_name = self._topic_name or self._CME_OPTIONS_BOOKS_TOPIC
        subscription_topic = f"projects/{self._CME_PROJECT_NAME}/topics/{topic_name}"
        client_name = f"book-binary-{topic_name.split('.')[-1]}"
        if params.poll_max_book:
            client_name = f"full-book-binary-{topic_name.split('.')[-1]}"
        params.diagnostics.info(f"Creating Google pub/sub client: {self._hostname}: {subscription_topic}")
        client = factory.google_pub_sub(self._client_json, subscription_topic, client_name, self._hostname)
        translator = CmeSmartStreamFixTranslator(self._template_path, message_name)
        return Stream(client, processor, translator, params.diagnostics)

    @staticmethod
    def strip_hostname(hostname: str) -> str:
        if hostname.startswith("ny5-") or hostname.startswith("hel1-"):
            # Strip the two hash entries from the end of the k8s pod name
            return "-".join(hostname.split("-")[:-2])
        # Leave legacy Hetzner hostnames alone
        return hostname


class _CmeMarketHoursComponent(IStreamProcessor[Optional[DecodedMessage]]):
    @staticmethod
    def _is_market_open(utc_dt: datetime, grace_period: timedelta) -> bool:
        market_dt = utc_dt.astimezone(MARKET_TZ)
        market_close = MARKET_TZ.localize(datetime(market_dt.year, market_dt.month, market_dt.day, MARKET_CLOSE_HOUR, 0, 0))
        market_open = MARKET_TZ.localize(datetime(market_dt.year, market_dt.month, market_dt.day, MARKET_OPEN_HOUR, 0, 0))
        # On or after market open (with grace period), Sunday through Thursday
        if market_dt >= market_open - grace_period and market_dt.weekday() not in [4, 5]:
            return True
        # On or before market close (with grace period), Monday through Friday
        if market_dt <= market_close + grace_period and market_dt.weekday() < 5:
            return True
        return False

    def __init__(self, message_type: str):
        self._message_type = message_type
        self._saturday_discard = 0

    def on_message(self, message: Optional[DecodedMessage], stream: IStreamApi[Optional[DecodedMessage]]) -> bool:
        """Returns True (stopping processing) if message arrival or trade time are during market close"""
        assert message is not None

        if self._saturday_discard > 0 and datetime.utcnow().weekday() != 5:
            stream.diagnostics.info(f"{self._message_type}: saturday embargo: discarded {self._saturday_discard} rows")
            self._saturday_discard = 0

        # The CME can/will send test data on the weekend -- only allow trade records if:
        # 1) The publication timestamp is during market hours (within the PUBLISH grace period)
        # 2) The trade timestamp is during market hours (within the TRADE grace period)
        # Note: pub/sub can cause old records to appear -- beware!
        if self._is_market_open(message.publish_time, PUBLISH_GRACE_PERIOD):
            return False
        trade_dt = dt_from_us_aware(message.value["TransactTime"] // 1000)
        if self._is_market_open(trade_dt, TRADE_GRACE_PERIOD):
            return False

        if datetime.utcnow().astimezone(MARKET_TZ).weekday() == 5:
            self._saturday_discard += 1
        else:
            stream.diagnostics.info(f"{self._message_type}: market closed: discarding: {message}")
        return True


class _TradeComponent(IStreamProcessor[Optional[DecodedMessage]]):
    def __init__(self, instruments: Instruments, on_trades: TradeCallback):
        exchange_id = glib_exchange().exchange_id_by_name(EXCHANGE_NAME)
        self._instruments_market_map = {
            instrument.metadata["security_id"]: Market(exchange_id, instrument) for instrument in instruments
        }
        self._on_trades = on_trades

    def on_message(self, message: Optional[DecodedMessage], stream: IStreamApi[Optional[DecodedMessage]]) -> bool:
        assert message is not None

        trades = []
        transact_time = message.value["TransactTime"]
        for trade in message.value["NoMDEntries"]:
            market = self._instruments_market_map.get(trade["SecurityID"], None)
            if market:
                display_factor = market.instrument.metadata["display_factor"]
                trades.append(Trade(market, _trade_from_message(trade, transact_time, display_factor)))

        try:
            if len(trades) > 0:
                self._on_trades(trades)
        except Exception as e:
            stream.diagnostics.error(e, "failure in on_trades")

        return True


def _trade_from_message(trade: Dict[str, Any], transact_time: int, display_factor: int) -> TradeData:
    return TradeData(
        trade_id=int(str(transact_time) + str(trade["MDTradeEntryID"])),
        amount=Decimal(trade["MDEntrySize"]),
        price=_parse_price(trade["MDEntryPx"], display_factor),
        is_buy=None if trade["AggressorSide"] == "NoAggressor" else trade["AggressorSide"] == "Buy",
        time=dt_from_us(transact_time // 1000),
    )


def _parse_price(entry_px: Dict[str, int], display_factor: int) -> Decimal:
    mantissa = Decimal(entry_px["mantissa"])
    exponent = entry_px["exponent"]
    if exponent == -9:
        return mantissa * Decimal("1e-9") / display_factor
    return (mantissa * (Decimal(10) ** exponent)) / display_factor


class _BookComponent(BookComponent[Optional[DecodedMessage]]):
    """Reassemble books from the multi-part SnapshotFullRefreshOrderBook53 messages"""

    def __init__(self, instruments: Instruments, params: BookStreamParams):
        super().__init__(EXCHANGE_NAME, _dummy_extract_instrument, _dummy_get_bookdata, params)
        exchange_id = glib_exchange().exchange_id_by_name(EXCHANGE_NAME)
        self._instruments_market_map = {inst.metadata["security_id"]: Market(exchange_id, inst) for inst in instruments}
        self._book_messages: Dict[int, _CmeBookChunkAssembler] = {}

    def on_message(self, message: Optional[DecodedMessage], stream: IStreamApi[Optional[DecodedMessage]]) -> bool:
        assert message is not None

        security_id = message.value["SecurityID"]
        market = self._instruments_market_map.get(security_id, None)
        if market is None:
            return True

        bma = self._book_messages.get(security_id, None)
        if not bma:
            bma = _CmeBookChunkAssembler(market.instrument, message)
            self._book_messages[security_id] = bma

        try:
            bma.insert_chunk(message)
        except (CMEMismatchTransactTimeException, CMEMismatchLastSeqNumException) as ex:
            stream.diagnostics.warning(f"{ex}: Dropping: {bma}")
            bma = _CmeBookChunkAssembler(market.instrument, message)
            self._book_messages[security_id] = bma
            bma.insert_chunk(message)
        except Exception as ex:
            stream.diagnostics.warning(f"{ex}: Dropping: {bma}")
            del self._book_messages[security_id]
            return True

        if bma.book_is_complete:
            self._handle_complete_book(bma, security_id, stream, market.instrument)

        return True

    def _handle_complete_book(
        self,
        bma: "_CmeBookChunkAssembler",
        security_id: int,
        stream: IStreamApi[Optional[DecodedMessage]],
        instrument: Instrument,
    ):
        book_data = bma.get_book_data()
        del self._book_messages[security_id]
        if len(book_data.bids) == 0 and len(book_data.asks) == 0:
            pass  # We drop empty books with no log message
        elif invalid_book := book_data.validate():
            stream.diagnostics.warning(f"Dropping invalid book {invalid_book}: {bma}")
        else:
            self.on_book(instrument, book_data, stream)


class _CmeBookChunkAssembler:
    """Collect each of the message chunks until we have the entire set, then convert to BookData"""

    def __init__(self, instrument: Instrument, message: DecodedMessage):
        self._display_factor: int = instrument.metadata["display_factor"]
        self._symbol: str = instrument.symbol
        self._security_id: int = message.value["SecurityID"]
        self._transact_time: int = message.value["TransactTime"]
        self._last_msg_seq_num: int = message.value["LastMsgSeqNumProcessed"]
        self._message_chunks: List[Optional[DecodedMessage]] = [None] * message.value["NoChunks"]

    def __repr__(self) -> str:
        chunks = [f"Msg{i}" if self._message_chunks[i] else "None" for i in range(len(self._message_chunks))]
        return f"{self._symbol}/{self._security_id}: {self._transact_time} {self._last_msg_seq_num} {chunks}"

    @property
    def book_is_complete(self) -> bool:
        return None not in self._message_chunks

    def insert_chunk(self, message: DecodedMessage) -> None:
        def _check_vals(x: int, y: int) -> Optional[str]:
            if x == y:
                return None
            if x > y:
                return f"{x} > {y}"
            return f"{x} < {y}"

        if error_message := _check_vals(message.value["TransactTime"], self._transact_time):
            raise CMEMismatchTransactTimeException(error_message)

        if error_message := _check_vals(message.value["LastMsgSeqNumProcessed"], self._last_msg_seq_num):
            raise CMEMismatchLastSeqNumException(error_message)

        current_chunk_idx = int(message.value["CurrentChunk"]) - 1
        if self._message_chunks[current_chunk_idx]:
            raise Exception(f"Duplicate chunk: {current_chunk_idx}")

        self._message_chunks[current_chunk_idx] = message

    def get_book_data(self) -> BookData:
        bids: List[PriceLevel] = []
        asks: List[PriceLevel] = []
        for message in self._message_chunks:
            assert message is not None
            for entry in message.value["NoMDEntries"]:
                pl = PriceLevel(
                    price=_parse_price(entry["MDEntryPx"], self._display_factor), amount=Decimal(entry["MDDisplayQty"]), count=1
                )
                entry_type = entry["MDEntryType"]
                if entry_type == "Bid":
                    bids.append(pl)
                elif entry_type == "Offer":
                    asks.append(pl)
                else:
                    raise Exception(f"Unexpected MDEntryType: {entry_type}")

        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=None,
            exchange_time=dt_from_us_aware(self._transact_time // 1000),
            bids=compress_price_levels(bids),
            asks=compress_price_levels(asks),
        )


def _dummy_extract_instrument(message: Optional[DecodedMessage]) -> Optional[Instrument]:
    raise NotImplementedError("Unimplemented extract_instrument")


def _dummy_get_bookdata(message: Optional[DecodedMessage]) -> BookData:
    raise NotImplementedError("Unimplemented get_bookdata")
