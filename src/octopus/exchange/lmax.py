import ssl
from datetime import datetime
from decimal import Decimal
from typing import Dict, Iterable, List, Optional, Tuple

import pytz
from pytz import timezone

from src.fixsocket.fix_translator import (
    HEARTBEAT,
    LOGON,
    LOGOUT,
    MARKET_DATA_REQUEST,
    MARKET_DATA_REQUEST_REJECT,
    MARKET_DATA_SNAPSHOT_FULL_REFRESH,
    MD_REQ_BOOK,
    MD_REQ_TRADE,
    REJECT,
    RESEND_REQUEST,
    SEQUENCE_RESET,
    TEST_REQUEST,
    FixTranslator,
)
from src.octopus.data import BookData, BookType, Instrument, PriceLevel, SpotContractData, TradeData
from src.octopus.exchange.api import BookStreamParams, ExchangeMarkets, IClientFactory, Instruments, TradeStreamParams
from src.octopus.exchange.api_utils import ExchangeHttpApiBase, ExchangeStreamingApiBase
from src.octopus.stream_components.book import BookComponent
from src.octopus.stream_components.error import ErrorHandlingComponent
from src.octopus.stream_components.subscription import SubscriptionComponent, SubscriptionManagerBase, SubscriptionUpdate
from src.octopus.stream_components.trade import TradeComponent
from src.octopus.translation import ChannelTranslator, ExchangeTickerTranslator
from src.utils.diagnostics import Diagnostics
from src.utils.execution import IRunnable, endless_retry_backoff
from src.utils.http import IHttpClient
from src.utils.socket import SslParams
from src.utils.stream import ComponentProcessor, IStreamApi, IStreamProcessor, Stream, StreamingClient, StreamParams
from src.utils.timeutil import dt_to_ms
from src.utils.trade_id import TradeId
from src.utils.types import JsonValue

EXCHANGE_NAME = "LMAX"

# Bijective map: ticker <-> LMAX orderbook id
_INSTRUMENT_MAP = {
    # Production instruments from https://www.lmaxdigital.com/documents/csv/LMAXDigital-Instruments.csv
    "BTC/USD": "5004",
    "ETH/USD": "5002",
    "LTC/USD": "5003",
    "BCH/USD": "5005",
    "XRP/USD": "5006",
    "ETH/BTC": "5013",
    "ETH/JPY": "5007",
    "LTC/JPY": "5010",
    "BTC/JPY": "5009",
    "BCH/JPY": "5008",
    "XRP/JPY": "5011",
    "BTC/EUR": "5018",
    "LTC/EUR": "5019",
    "XRP/EUR": "5020",
    "ETH/EUR": "5016",
    "BCH/EUR": "5017",
    "BTC/GBP": "5021",
    "ETH/GBP": "5022",
    "BCH/GBP": "5023",
    "XRP/GBP": "5024",
    "LTC/GBP": "5025",
    "SOL/USD": "5026",
    # "MATIC/USD": "5055", # currently it is not in the list of LMAX instruments
    "LINK/USD": "5056",
    "USDC/USD": "5031",
    "PYTH/USD": "5062",
    "BTC/USDm": "5029",
    "ETH/USDm": "5030",
    "BTC/EURm": "5070",
    "ETH/EURm": "5071",
    "UNI/USD": "5074",
    "AAVE/USD": "5076",
    "DOGE/USD": "5078",
    "ETH/RLUSD": "5080",
    "BTC/RLUSD": "5081",
    "XRP/RLUSD": "5082",
    "RLUSD/USD": "5083",
    # 'FUN/KEY': '6666',      # 6666	test instrument1   PRT      INDEX   <<Note: ticks on UAT1, good for testing>>
}

_METADATA_MAP = {
    "BTC/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2018, 2, 5, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "ETH/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2018, 2, 5, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "LTC/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2018, 2, 5, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BCH/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2018, 2, 5, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "XRP/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2018, 2, 5, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "ETH/BTC": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 1, 4, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "ETH/JPY": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 3, 19, tzinfo=timezone("utc")),
        "price_increment": Decimal("1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "LTC/JPY": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 3, 19, tzinfo=timezone("utc")),
        "price_increment": Decimal("1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BTC/JPY": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 3, 19, tzinfo=timezone("utc")),
        "price_increment": Decimal("1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BCH/JPY": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 3, 19, tzinfo=timezone("utc")),
        "price_increment": Decimal("1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "XRP/JPY": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 3, 19, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "BTC/EUR": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 7, 25, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "LTC/EUR": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 7, 25, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "XRP/EUR": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 7, 25, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "ETH/EUR": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 7, 25, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BCH/EUR": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2019, 7, 25, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BTC/GBP": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2021, 7, 16, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "ETH/GBP": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2021, 7, 16, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BCH/GBP": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2021, 7, 16, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "XRP/GBP": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2021, 7, 16, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "LTC/GBP": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2021, 7, 16, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "SOL/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2022, 2, 3, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "LINK/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2023, 5, 2, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.1"),
        "amount_size_min": Decimal("0.1"),
    },
    "USDC/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2023, 6, 30, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "PYTH/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2023, 12, 4, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.0001"),
        "amount_increment": Decimal("0.1"),
        "amount_size_min": Decimal("0.1"),
    },
    "BTC/USDm": {
        "contract_size": Decimal("0.01"),
        "listing_date": datetime(2022, 2, 23, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "ETH/USDm": {
        "contract_size": Decimal("0.01"),
        "listing_date": datetime(2022, 2, 23, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BTC/EURm": {
        "contract_size": Decimal("0.01"),
        "listing_date": datetime(2024, 4, 10, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "ETH/EURm": {
        "contract_size": Decimal("0.01"),
        "listing_date": datetime(2024, 4, 10, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.1"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "UNI/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2025, 3, 3, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "AAVE/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2025, 3, 3, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.01"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "DOGE/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2025, 3, 3, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "ETH/RLUSD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2025, 3, 18, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "BTC/RLUSD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2025, 3, 18, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.001"),
        "amount_increment": Decimal("0.01"),
        "amount_size_min": Decimal("0.01"),
    },
    "XRP/RLUSD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2025, 3, 18, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
    "RLUSD/USD": {
        "contract_size": Decimal("1"),
        "listing_date": datetime(2025, 3, 18, tzinfo=timezone("utc")),
        "price_increment": Decimal("0.00001"),
        "amount_increment": Decimal("1"),
        "amount_size_min": Decimal("1"),
    },
}


_CREDENTIALS = {
    "staging": {
        "trades": [
            {
                "address": "fix-marketdata.digital-uat.lmax.com:443",
                "username": "coinmetricsdigUAT1",
                "password": "coinmetricsdigUAT1",
                "sender_comp_id": "coinmetricsdigUAT1",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.digital-uat.lmax.com:443",
                "username": "coinmetricsdigUAT4",
                "password": "coinmetricsdigUAT4",
                "sender_comp_id": "coinmetricsdigUAT4",
                "target_comp_id": "LMXBLM",
            },
        ],
        "books": [
            {
                "address": "fix-marketdata.digital-uat.lmax.com:443",
                "username": "coinmetricsdigUAT5",
                "password": "coinmetricsdigUAT5",
                "sender_comp_id": "coinmetricsdigUAT5",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.digital-uat.lmax.com:443",
                "username": "coinmetricsdigUAT6",
                "password": "coinmetricsdigUAT6",
                "sender_comp_id": "coinmetricsdigUAT6",
                "target_comp_id": "LMXBLM",
            },
        ],
    },
    "production": {
        "trades": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD",
                "password": "xdSZY@$j%bJtGW3v",
                "sender_comp_id": "coinmetricsMD",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD2",
                "password": "R]QNyD&4d`av-{]J",
                "sender_comp_id": "coinmetricsMD2",
                "target_comp_id": "LMXBLM",
            },
        ],
        "books": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD3",
                "password": "coinmetricsMD3",
                "sender_comp_id": "coinmetricsMD3",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD4",
                "password": "coinmetricsMD4",
                "sender_comp_id": "coinmetricsMD4",
                "target_comp_id": "LMXBLM",
            },
        ],
    },
    "hel1": {
        "trades": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD5",
                "password": "coinmetricsMD5",
                "sender_comp_id": "coinmetricsMD5",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD6",
                "password": "coinmetricsMD6",
                "sender_comp_id": "coinmetricsMD6",
                "target_comp_id": "LMXBLM",
            },
        ],
        "books": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD7",
                "password": "coinmetricsMD7",
                "sender_comp_id": "coinmetricsMD7",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD8",
                "password": "coinmetricsMD8",
                "sender_comp_id": "coinmetricsMD8",
                "target_comp_id": "LMXBLM",
            },
        ],
    },
    "ny5": {
        "trades": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD9",
                "password": "coinmetricsMD9",
                "sender_comp_id": "coinmetricsMD9",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD10",
                "password": "coinmetricsMD10",
                "sender_comp_id": "coinmetricsMD10",
                "target_comp_id": "LMXBLM",
            },
        ],
        "books": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD11",
                "password": "coinmetricsMD11",
                "sender_comp_id": "coinmetricsMD11",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD12",
                "password": "coinmetricsMD12",
                "sender_comp_id": "coinmetricsMD12",
                "target_comp_id": "LMXBLM",
            },
        ],
    },
    "cp1": {
        "trades": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD13",
                "password": "coinmetricsMD13",
                "sender_comp_id": "coinmetricsMD13",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD14",
                "password": "coinmetricsMD14",
                "sender_comp_id": "coinmetricsMD14",
                "target_comp_id": "LMXBLM",
            },
        ],
        "books": [
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD15",
                "password": "coinmetricsMD15",
                "sender_comp_id": "coinmetricsMD15",
                "target_comp_id": "LMXBLM",
            },
            {
                "address": "fix-marketdata.london-digital.lmax.com:443",
                "username": "coinmetricsMD16",
                "password": "coinmetricsMD16",
                "sender_comp_id": "coinmetricsMD16",
                "target_comp_id": "LMXBLM",
            },
        ],
    },
    "local": {
        "trades": [
            {
                "address": "fix-marketdata.digital-uat.lmax.com:443",
                "username": "coinmetricsdigUAT3",
                "password": "coinmetricsdigUAT3",
                "sender_comp_id": "coinmetricsdigUAT3",
                "target_comp_id": "LMXBLM",
            },
        ],
        "books": [
            {
                "address": "fix-marketdata.digital-uat.lmax.com:443",
                "username": "coinmetricsdigUAT3",
                "password": "coinmetricsdigUAT3",
                "sender_comp_id": "coinmetricsdigUAT3",
                "target_comp_id": "LMXBLM",
            },
        ],
    },
}

_TRADE_ID_CHANGE_TS = datetime(year=2022, month=7, day=20)


class LmaxHttpApi(ExchangeHttpApiBase):
    def spot_markets(self, client: IHttpClient) -> ExchangeMarkets:
        symbols = _INSTRUMENT_MAP.keys()
        return ExchangeMarkets([Instrument.spot(symbol, *symbol.lower().split("/")) for symbol in symbols])

    def spot_markets_metadata(self, client: IHttpClient) -> List[SpotContractData]:
        result: List[SpotContractData] = []
        for symbol in _INSTRUMENT_MAP:
            base_native, quote_native = symbol.split("/")
            base, quote = base_native.lower(), quote_native.lower()

            contract_data = SpotContractData(
                symbol=symbol,
                base_id=self.translator.to_cm_id(base),
                quote_id=self.translator.to_cm_id(quote),
                base_name=self.translator.translate(base),
                quote_name=self.translator.translate(quote),
                native_base_name=base_native,
                native_quote_name=quote_native,
                listing_date=_METADATA_MAP[symbol]["listing_date"],
                end_date=None,
                is_current=True,
                amount_increment=_METADATA_MAP[symbol]["amount_increment"],
                amount_size_min=_METADATA_MAP[symbol]["amount_size_min"],
                price_increment=_METADATA_MAP[symbol]["price_increment"],
                contract_size=_METADATA_MAP[symbol]["contract_size"],
            )
            result.append(contract_data)
        return result


class LmaxWebSocketApi(ExchangeStreamingApiBase):
    def __init__(
        self, exchange_name: str, ticker_translator: ExchangeTickerTranslator, client: IHttpClient, credential_config: str = ""
    ):
        super().__init__(exchange_name, ticker_translator, client)
        self._credential_deployment_type, self._credential_index = self._parse_credentials(credential_config)
        self._heartbeat_interval = 30
        self._trade_id = TradeId(max_len=10_000, max_age_seconds=5 * 60)

    def _parse_credentials(self, config: str) -> Tuple[str, int]:
        if config == "local":
            return "local", 0

        if (
            config.startswith("scrapers-production-")
            or config.startswith("feed-handlers-prod-")
            or config.startswith("fh-books-prod-")
        ):
            deployment = "production"
        elif config.startswith("scrapers-staging-"):
            deployment = "staging"
        else:
            deployment = config.split("-")[0]

        if deployment not in _CREDENTIALS:
            raise ValueError(f"Invalid configuration: {config}")

        machine, cm_config = config.split(":")
        cluster_machines = cm_config.split(",")

        return deployment, cluster_machines.index(machine)

    def _get_credentials(self, data_type: str, diagnostics: Diagnostics) -> Dict[str, str]:
        credentials = _CREDENTIALS[self._credential_deployment_type][data_type][self._credential_index]
        diagnostics.info(f"Using {credentials['username']} at {credentials['address']}")
        return credentials

    def trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        credentials = self._get_credentials("trades", params.diagnostics)

        client = self._get_client(factory, credentials["address"])
        connection = _ConnectionProcessor(credentials["username"], credentials["password"], self._heartbeat_interval)
        self._channel_translator = ChannelTranslator(instruments, lambda instrument: _INSTRUMENT_MAP[instrument.symbol])

        processor = ComponentProcessor(
            connection,
            ErrorHandlingComponent(_is_error_message, reconnect_on_error=True),
            _HeartbeatProcessor(),
            _SequenceProcessor(),
            SubscriptionComponent(
                instruments,
                _SubscriptionManager(MD_REQ_TRADE, None, self._channel_translator),
                params.scraped_market_count,
                timeout=5 * self._heartbeat_interval,
                batch_subscription=True,
                can_subscribe=connection.is_connected,
            ),
            TradeComponent(EXCHANGE_NAME, self._extract_instrument, self._extract_trades, params.on_trades),
        )

        lmax_translator = FixTranslator(credentials["sender_comp_id"], credentials["target_comp_id"])
        stream_params = StreamParams(connect_retry=endless_retry_backoff(1.0), tick_interval=self._heartbeat_interval)
        return Stream(client, processor, lmax_translator, params.diagnostics, stream_params)

    def _extract_trades(self, message: JsonValue) -> List[TradeData]:
        return [trade_data for trade_data in self._trades_from_message(message)]

    def _trades_from_message(self, message: JsonValue) -> Iterable[TradeData]:
        for trade in message["trades"]:
            size = Decimal(trade["size"])
            amount = abs(size)
            is_buy = size > 0
            price = Decimal(trade["price"])
            timestamp = datetime.strptime(f"{trade['date']} {trade['time']}", "%Y%m%d %H:%M:%S.%f")
            instrument = message["symbol"]
            trade_id = self._trade_id.make_trade_id(instrument, str(dt_to_ms(timestamp)), price, amount, is_buy)
            yield TradeData(trade_id, amount, price, is_buy, timestamp)

    def books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        credentials = self._get_credentials("books", params.diagnostics)

        depth = min(params.depth, 20)
        client = self._get_client(factory, credentials["address"])
        connection = _ConnectionProcessor(credentials["username"], credentials["password"], self._heartbeat_interval)
        self._channel_translator = ChannelTranslator(instruments, lambda instrument: _INSTRUMENT_MAP[instrument.symbol])

        processor = ComponentProcessor(
            connection,
            ErrorHandlingComponent(_is_error_message, reconnect_on_error=True),
            _HeartbeatProcessor(),
            _SequenceProcessor(),
            SubscriptionComponent(
                instruments,
                _SubscriptionManager(MD_REQ_BOOK, depth, self._channel_translator),
                params.scraped_market_count,
                timeout=5 * self._heartbeat_interval,
                batch_subscription=True,
                can_subscribe=connection.is_connected,
            ),
            BookComponent(EXCHANGE_NAME, self._extract_instrument, _books_from_message, params),
        )

        lmax_translator = FixTranslator(credentials["sender_comp_id"], credentials["target_comp_id"])
        stream_params = StreamParams(connect_retry=endless_retry_backoff(1.0), tick_interval=self._heartbeat_interval)
        return Stream(client, processor, lmax_translator, params.diagnostics, stream_params)

    def _extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if message["message_type"] == MARKET_DATA_SNAPSHOT_FULL_REFRESH:
            return self._channel_translator.to_instrument(message["symbol"])
        return None

    def _get_client(self, factory: IClientFactory, address: str) -> StreamingClient[JsonValue]:
        ssl_params: SslParams = SslParams(version=ssl.PROTOCOL_TLSv1_2, cert_reqs=ssl.CERT_NONE)
        return factory.fixsocket(address, ssl_params)


class _ConnectionProcessor(IStreamProcessor[JsonValue]):
    def __init__(self, username: str, password: str, heartbeat_interval: int) -> None:
        self._username: str = username
        self._password: str = password
        self._heartbeat: int = heartbeat_interval
        self._connection_confirmed: bool = False

    def on_open(self, stream: IStreamApi[JsonValue]) -> None:
        self._connection_confirmed = False
        stream.send({
            "message_type": LOGON,
            "heartbeat": self._heartbeat,
            "username": self._username,
            "password": self._password,
        })

    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        message_type = message["message_type"]
        if message_type == LOGON:
            stream.diagnostics.info("Login successful.")
            self._connection_confirmed = True
            return True

        if message_type == LOGOUT:
            self._connection_confirmed = False
            logout_text = message["message"]
            stream.diagnostics.warning(f"Logout request: {logout_text}")
            return False  # ErrorHandlingComponent takes care of the reconnect

        return False

    def is_connected(self) -> bool:
        return self._connection_confirmed


def _is_error_message(message: JsonValue) -> bool:
    message_type = message["message_type"]
    return message_type in [REJECT, RESEND_REQUEST, MARKET_DATA_REQUEST_REJECT, LOGOUT]


class _HeartbeatProcessor(IStreamProcessor[JsonValue]):
    def on_tick(self, time_passed: float, stream: IStreamApi[JsonValue]) -> None:
        if time_passed < 15.0:  # skip first on_tick(0.0)
            return

        stream.send({
            "message_type": HEARTBEAT,
        })

    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        message_type = message["message_type"]
        if message_type == HEARTBEAT:
            return True

        if message_type == TEST_REQUEST:
            stream.diagnostics.warning(f"Test request received: {message}.")
            stream.send({"message_type": HEARTBEAT, "test_message": message["test_message"]})
            return True

        return False


class _SequenceProcessor(IStreamProcessor[JsonValue]):
    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        message_type = message["message_type"]
        if message_type == SEQUENCE_RESET:
            stream.diagnostics.warning(f"Sequence reset {message}.")
            return True

        return False


class _SubscriptionManager(SubscriptionManagerBase):
    def __init__(self, channel_type: str, book_depth: Optional[int], translator: ChannelTranslator):
        super().__init__(translator)
        self._channel_type = channel_type
        self._book_depth = book_depth
        self._instruments_to_confirm: List[Instrument] = []

    def reset(self) -> None:
        self._instruments_to_confirm = []

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self._instruments_to_confirm = instruments
        return {
            "message_type": MARKET_DATA_REQUEST,
            "request_type": self._channel_type,
            "book_depth": self._book_depth,
            "instruments": [self.translator.to_channel(instrument) for instrument in instruments],
        }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        # FIX has no positive acknowledgement for a MARKET_DATA_REQUEST (it does however reject bad requests),
        # so we create a positive confirmation when the next non-error message arrives on the feed.
        instruments = self._instruments_to_confirm
        if len(instruments) == 0:
            return None
        self._instruments_to_confirm = []
        return SubscriptionUpdate(positive_confirmations=instruments, negative_confirmations=[])


def _books_from_message(message: JsonValue) -> BookData:
    book = message["book"]

    book_type = BookType.FULL  # LMAX confirms every message is a full book (no updates)
    bids = [
        PriceLevel(price=Decimal(price), amount=Decimal(amount))
        for price, amount in zip(book["bid_prices"], book["bid_amounts"])
    ]
    asks = [
        PriceLevel(price=Decimal(price), amount=Decimal(amount))
        for price, amount in zip(book["ask_prices"], book["ask_amounts"])
    ]
    if len(bids) == 0 and len(asks) == 0:
        timestamp = None
    else:
        timestamp = datetime.strptime(f"{book['date']} {book['time']}", "%Y%m%d %H:%M:%S.%f")
        timestamp = pytz.utc.localize(timestamp)

    return BookData(book_type, None, timestamp, bids, asks)
