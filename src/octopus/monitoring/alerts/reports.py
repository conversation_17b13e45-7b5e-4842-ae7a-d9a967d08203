import re
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, Iterable, List, Optional, Tuple, Type

import requests

from src.octopus.applications.dashboards_updater import GRAFANA_CONFIGS
from src.octopus.generators.dashboards.common import GrafanaConfig
from src.octopus.inventory.types import ConnectionMode, DataType, MarketType
from src.octopus.monitoring.alerts.known_alerts import is_known_alert
from src.octopus.monitoring.alerts.models import Alert, AlertPriority, ReportEntry
from src.octopus.monitoring.errors import MonitoringError
from src.octopus.monitoring.models import DataCenter, MonitoringLevel
from src.octopus.monitoring.sources.k8s import MONITORING_OWNER, PodSource

MappedAlerts = Dict[MonitoringLevel, Dict[str, Dict[Type[MonitoringError], Dict[str, ReportEntry]]]]
CONTAINER_NAME_PARSE_RE = re.compile(
    r"feed-handler-(.*?)-(book|trad|open|liqu|fund|quot|meta|tick)-(spot|futu|opti)-(stre|http|hist)(.*?)-(\d)\Z"
)
DATA_LOADER_CONTAINER_NAME_PARSE_RE = re.compile(r"feed-handler-(.*?)-(book|trad|open|liqu|fund|quot|meta|tick)-loader-(\d)")
GRAFANA_TITLE_PARSE_RE = re.compile(r"(\w+) (.*) (HTTP|Streaming|History) (Spot|Futures|Option) (.*)")


class BackOffValues:
    FIRING_PERIOD = timedelta(minutes=1)
    INTERVAL = timedelta(hours=1)

    def __init__(self) -> None:
        _now = datetime.now()
        self.next_fire = datetime(year=_now.year, month=_now.month, day=_now.day, hour=_now.hour, minute=_now.minute)
        self.firing_at = self.next_fire - self.INTERVAL

    def next(self) -> bool:
        _now = datetime.now()
        if self.next_fire < _now:
            self.next_fire += self.INTERVAL
            self.firing_at = _now
            return True
        elif _now - self.firing_at < self.FIRING_PERIOD:
            return True
        else:
            return False


class Alerts(Dict[str, ReportEntry]):
    def __init__(self, mapping: Any = None, /, **kwargs: Any) -> None:
        mapping = mapping or {}
        if kwargs:
            mapping.update(kwargs)
        super().__init__(mapping)

    def map(self, alert: Alert, data_center: DataCenter, prev: Optional[ReportEntry]) -> None:
        self[alert.key] = ReportEntry(
            alert=alert,
            first_fired=datetime.utcnow() if not prev else prev.first_fired,
            last_fired=datetime.utcnow(),
            is_known=is_known_alert(alert, data_center),
            is_new=not prev,
            grafana_url=GrafanaUrls().get_container_detailed_view(alert.container_name, data_center),
        )

    def iter(self, filter_func: Optional[Callable[[ReportEntry], bool]] = None) -> Iterable[ReportEntry]:
        for alert_key in self:
            entry = self[alert_key]
            if not filter_func or filter_func(entry):
                yield entry

    def filter(self, filter_func: Optional[Callable[[ReportEntry], bool]] = None) -> "Alerts":
        return Alerts({entry.alert.key: entry for entry in self.iter(filter_func)})

    def len(self, filter_func: Optional[Callable[[ReportEntry], bool]] = None) -> int:
        return len(list(self.iter(filter_func))) if filter_func else len(self)

    def mapped_alerts(self) -> MappedAlerts:
        mapped: MappedAlerts = {}
        for alert_key in self:
            entry = self[alert_key]
            alert = entry.alert

            if alert.error_level not in mapped:
                mapped[alert.error_level] = {}

            if alert.data_type not in mapped[alert.error_level]:
                mapped[alert.error_level][alert.data_type] = {}

            if alert.error_type not in mapped[alert.error_level][alert.data_type]:
                mapped[alert.error_level][alert.data_type][alert.error_type] = {}

            mapped[alert.error_level][alert.data_type][alert.error_type][alert_key] = entry
        return mapped


class GrafanaUrls:
    MAP_KEY_TYPE = Tuple[str, str, DataType, MarketType, ConnectionMode, str]
    urls: Dict[MAP_KEY_TYPE, str] = {}
    INTERNAL_GRAFANA_URL = "http://grafana.monitoring.svc"

    def get_container_detailed_view(self, container_name: str, data_center: DataCenter) -> str:
        if not self.urls:
            grafana_config = GRAFANA_CONFIGS.get(DataCenter.deployment_type(data_center))
            if grafana_config:
                self._init_grafana_urls(grafana_config)
            else:
                raise ValueError(f"Grafana config missed for data center: {data_center}")

        keys = self._get_map_key_from_container_name(container_name)

        for key in keys:
            if key in self.urls:
                return self.urls[key]
        return ""

    def _init_grafana_urls(self, grafana_config: GrafanaConfig) -> None:
        folders_map = self.get_grafana_folders(grafana_config)
        folder_name = grafana_config.folders[0][0]
        folder_id = folders_map.get(folder_name)
        if folder_id is None:
            raise ValueError(f"Grafana folder not mapped: {folder_name}")

        dashboards = self.get_folder_dashboards(grafana_config, folder_id)
        for dashboard in dashboards:
            dashboard_url = dashboard["url"]
            keys = self._get_map_key_from_grafana_dashboard_title(dashboard["title"])
            for key in keys:
                if key not in self.urls:
                    self.urls[key] = f"{grafana_config.instance.url}{dashboard_url}"

    def get_folder_dashboards(self, grafana_config: GrafanaConfig, folder_id: int) -> Any:
        return self.try_request(
            grafana_config,
            urls=[
                f"http://grafana.monitoring.svc/api/search?folderIds={folder_id}",
                f"{grafana_config.instance.url}/api/search?folderIds={folder_id}",
            ],
        )

    def get_grafana_folders(self, grafana_config: GrafanaConfig) -> Dict[str, int]:
        folders = self.try_request(
            grafana_config,
            urls=["http://grafana.monitoring.svc/api/folders", f"{grafana_config.instance.url}/api/folders"],
        )
        return {folder["title"].strip(): folder["id"] for folder in folders}

    def try_request(self, grafana_config: GrafanaConfig, urls: List[str]) -> Any:
        for url in urls:
            try:
                return requests.get(url, headers=self._request_headers(grafana_config)).json()
            except Exception:
                pass
        raise ValueError(f"Grafana request failed: {urls}")

    @staticmethod
    def _request_headers(grafana_config: GrafanaConfig) -> Dict[str, str]:
        return {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {grafana_config.instance.key}",
            "Host": grafana_config.instance.url.replace("http://", "").replace("https://", ""),
        }

    def _get_map_key_from_container_name(self, container_name: str) -> List[MAP_KEY_TYPE]:
        if "loader" in container_name:
            return self._get_map_key_for_data_loader(container_name)
        else:
            parts = re.findall(CONTAINER_NAME_PARSE_RE, container_name)
        if not parts:
            raise ValueError(f"Couldn't parse container name: {container_name}")

        exchange_name_str, data_type_str, market_type_str, connection_mode_str, segment_suffix, _ = parts[0]
        exchange_name = exchange_name_str.split("-")[0].lower()
        exchange_suffix = exchange_name_str.lower().replace(exchange_name, "").replace("-", "").strip()

        for _connection_mode in ConnectionMode:
            if _connection_mode.m_short_name == connection_mode_str:
                connection_mode = _connection_mode
                break
        else:
            raise ValueError(f"Couldn't find connection mode for: {connection_mode_str}")

        for _market_type in MarketType:
            if _market_type.m_short_name == market_type_str:
                market_type = _market_type
                break
        else:
            raise ValueError(f"Couldn't find market type for: {market_type_str}")

        for _data_type in DataType:
            if _data_type.m_short_name == data_type_str:
                data_type = _data_type
                break
        else:
            raise ValueError(f"Couldn't find data type for: {data_type_str}")

        segment_suffix = segment_suffix.strip("-") if segment_suffix else ""
        return [
            (exchange_name, exchange_suffix, data_type, market_type, connection_mode, segment_suffix),
            (exchange_name, f"{exchange_suffix} {segment_suffix}", data_type, market_type, connection_mode, ""),
        ]

    @staticmethod
    def _get_map_key_for_data_loader(container_name: str) -> List[MAP_KEY_TYPE]:
        data_loader_container_name_parts = re.findall(DATA_LOADER_CONTAINER_NAME_PARSE_RE, container_name)
        if not data_loader_container_name_parts:
            return []

        exchange_name_str, data_type_str, _ = data_loader_container_name_parts[0]

        for _data_type in DataType:
            if _data_type.m_short_name == data_type_str:
                data_type = _data_type
                break
        else:
            raise ValueError(f"Couldn't find data type for: {data_type_str}")

        keys = []
        for _market_type in MarketType:
            for _connection_mode in ConnectionMode:
                keys.append((exchange_name_str, "", data_type, _market_type, _connection_mode, ""))
        return keys

    @staticmethod
    def _get_map_key_from_grafana_dashboard_title(dashboard_title: str) -> List[MAP_KEY_TYPE]:
        parts = re.findall(GRAFANA_TITLE_PARSE_RE, dashboard_title)
        if not parts:
            raise ValueError(f"Couldn't parse dashboard title: {dashboard_title}")

        _, exchange_parts_str, connection_mode_str, market_type_str, segment_suffix_str = parts[0]
        exchange_parts_str = exchange_parts_str.replace(".", "")
        exchange_name = exchange_parts_str.split()[0].replace(" ", "").lower()
        exchange_suffix = exchange_parts_str.lower().replace(exchange_name, "").replace("-", "").strip()

        for _connection_mode in ConnectionMode:
            if _connection_mode.name.lower() == connection_mode_str.lower():
                connection_mode = _connection_mode
                break
        else:
            raise ValueError(f"Couldn't find connection mode for: {connection_mode_str}")

        for _market_type in MarketType:
            if _market_type.name.lower() == market_type_str.lower():
                market_type = _market_type
                break
        else:
            raise ValueError(f"Couldn't find market type for: {market_type_str}")

        data_type_str, *segment_parts = segment_suffix_str.split("-")
        for _data_type in DataType:
            if _data_type.name.lower() == data_type_str[:-1].replace(" ", "_").lower():
                data_type = _data_type
                break
        else:
            raise ValueError(f"Couldn't find data type for: {data_type_str}")

        segment_suffix = "-".join(segment_parts)
        return [(exchange_name, exchange_suffix, data_type, market_type, connection_mode, segment_suffix)]


class ReportBuilder:
    def __init__(self, pods: PodSource) -> None:
        self._pods = pods
        self._back_off_map: Dict[str, Dict[str, Any]] = {}

    def build_full_report(
        self, alerts: Alerts, data_center: DataCenter, instance_number: int, internal_errors: List[str]
    ) -> str:
        b = self._block
        mapped = alerts.filter(lambda entry: not entry.is_known).mapped_alerts()

        parts = []
        main_report = "\n\n".join([
            b(
                "\n\n",
                f"*{error_level.capitalize()} level errors*",
                [
                    b(
                        "\n\n",
                        f" *{data_type.capitalize()}*:",
                        [
                            b(
                                "\n",
                                f"  {self._get_first_alert_message(mapped[error_level][data_type][error])}:",
                                [
                                    "\n".join(
                                        sorted(
                                            self.build_alert_short_report(entry)
                                            for key, entry in mapped[error_level][data_type][error].items()
                                        )
                                    )
                                ],
                            )
                            for error in mapped[error_level][data_type]
                        ],
                    )
                    for data_type in mapped[error_level]
                ],
            )
            for error_level in mapped
        ])
        if main_report:
            parts.append(main_report)

        known_alerts_count = alerts.len(lambda entry: entry.is_known)
        if known_alerts_count > 0:
            parts.append(
                f"_{known_alerts_count} known messages are hidden. Click here for the details:_ "
                f"<{self._get_grafana_url(data_center)}|dashboard>"
            )

        if internal_errors:
            internal_errors_message = (
                f"*{len(internal_errors)} Internal errors occurred on this iteration.* "
                f"E.g.: {internal_errors[0]} cc: <@{MONITORING_OWNER.slack_id}>"
            )
            parts.append(internal_errors_message)

        report = "\n".join(parts)
        if not report:
            return f"FULL REPORT (instance {instance_number}): No alerts fired"
        else:
            return (
                f"------------FULL REPORT (instance {instance_number})-----------------\n"
                f"{report}\n"
                f"------------------------------------------------------------"
            )

    def build_alert_short_report(self, entry: ReportEntry) -> str:
        if entry.is_known:
            return ""
        return f"   - [{entry.time_code}] {self._container_name_with_link(entry)}{self._responsible_person_tag(entry)}"

    def build_alert_report(self, entry: ReportEntry) -> str:
        if entry.is_known:
            return ""
        return (
            f"[{entry.time_code}] {entry.alert.message}"
            f" - {self._container_name_with_link(entry)}{self._responsible_person_tag(entry)}"
        )

    def build_recovered_alert_report(self, entry: ReportEntry) -> str:
        if entry.is_known:
            return ""
        return (
            f"[*Recovered* after {entry.time_code}] {entry.alert.message}"
            f" - {self._container_name_with_link(entry)}{self._responsible_person_tag(entry)}"
        )

    @staticmethod
    def _container_name_with_link(entry: ReportEntry) -> str:
        url = entry.grafana_url
        container_name = entry.alert.container_name
        return f"<{url}|{container_name}>" if url else container_name

    def _responsible_person_tag(self, entry: ReportEntry) -> str:
        if entry.alert.priority != AlertPriority.HIGH:
            return ""

        user = self._pods.get_responsible_user(entry.alert.container_name)
        if user.name not in self._back_off_map:
            self._back_off_map[user.name] = {}

        back_off_values = self._back_off_map[user.name].get(entry.alert.data_type) or BackOffValues()
        tag_phrase = f" cc: <@{user.slack_id}>" if back_off_values.next() or entry.is_new else f" cc: {user.slack_name}"
        self._back_off_map[user.name][entry.alert.data_type] = back_off_values
        return tag_phrase

    def _responsible_person_info_tag(self, entry: ReportEntry) -> str:
        if entry.alert.priority != AlertPriority.HIGH:
            return ""

        user = self._pods.get_responsible_user(entry.alert.container_name)
        return f" cc: {user.slack_name}"

    @staticmethod
    def _block(separator: str, title: str, items: List[str]) -> str:
        if not items:
            return ""
        return f"{title}{separator}{separator.join(items)}"

    @staticmethod
    def _get_first_alert_message(alerts: Dict[str, ReportEntry]) -> str:
        return list(alerts.values())[0].alert.message

    @staticmethod
    def _get_grafana_url(data_center: DataCenter) -> str:
        if data_center == DataCenter.CDEV1:
            return "https://grafana.cdev1.cnmtrcs.io/d/eejz9xmn6kcu8e/fh-monitoring"
        elif data_center == DataCenter.CP1:
            return "https://grafana.cp1.cnmtrcs.io/d/ddpgcq37zsg74f/fh-monitoring"
        else:
            return f"FHS Monitoring dashboard URL is unknown url for {data_center} data center."
