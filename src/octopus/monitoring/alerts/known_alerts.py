import re
from datetime import datetime

from src.octopus.monitoring.alerts.models import Alert
from src.octopus.monitoring.models import DataCenter

KNOWN_ALERTS = {
    DataCenter.CDEV1: [
        re.compile(r"No data in Kafka:feed-handler-binance-quot-spot-stre-\d+-\d+-\d+"),
        re.compile(r"No data in Kafka:feed-handler-bitfinex-nexousd-book-spot-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-bitfinex-nexousddb-book-spot-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-deribit-trad-opti-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-deribit-liqu-futu-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-erisx-.*-spot-.*"),
        re.compile(r"No data in Postgres:feed-handler-deribit-trad-opti-stre-\d+"),
        re.compile(r"No data in Postgres:feed-handler-deribit-open-\d+"),
        re.compile(r"No data in Postgres:feed-handler-deribit-liqu-futu-\d+"),
        re.compile(r"No data in Postgres:feed-handler-huobi-1h-book-futu-stre-\d+-\d+-\d+"),
        re.compile(r"No data in Postgres:feed-handler-erisx-.*-spot-.*"),
        re.compile(r"Errors > \d+ \[\d+m]:feed-handler-kucoin-db-book-spot-http-\d+"),
        re.compile(r"Errors > \d+ \[\d+m]:feed-handler-binance-.*trad-futu-hist-\d+"),
        re.compile(r"Errors > \d+ \[\d+m]:feed-handler-bitfinex-trad-.*-hist-\d+"),
        re.compile(r"Logs > \d+MB \[\d+m]:feed-handler-bitfinex-trad-.*-hist-\d+"),
    ],
    DataCenter.CP1: [
        re.compile(r"No data in Kafka:feed-handler-binance-quot-spot-stre-\d+-\d+-\d+"),
        re.compile(r"No data in Kafka:feed-handler-bitfinex-nexousd-book-spot-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-bitfinex-nexousddb-book-spot-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-deribit-trad-opti-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-deribit-liqu-futu-stre-\d+"),
        re.compile(r"No data in Kafka:feed-handler-erisx-.*-spot-.*"),
        re.compile(r"No data in Postgres:feed-handler-deribit-trad-opti-stre-\d+"),
        re.compile(r"No data in Postgres:feed-handler-deribit-open-\d+"),
        re.compile(r"No data in Postgres:feed-handler-deribit-liqu-futu-\d+"),
        re.compile(r"No data in Postgres:feed-handler-erisx-.*-spot-.*"),
        re.compile(r"Errors > \d+ \[\d+m]:feed-handler-binance-trad-futu-hist-\d+"),
        re.compile(r"Errors > \d+ \[\d+m]:feed-handler-kucoin-db-book-spot-http-\d+"),
        re.compile(r"Errors > \d+ \[\d+m]:feed-handler-bitfinex-trad-.*-hist-\d+"),
        re.compile(r"Logs > \d+MB \[\d+m]:feed-handler-bitfinex-trad-.*-hist-\d+"),
    ],
}
CME_CONTAINERS_RE = re.compile(r".*-cme-.*")


def is_known_alert(alert: Alert, data_center: DataCenter) -> bool:
    now = datetime.utcnow()
    return is_suspended_due_to_schedule(alert, now) or any(re.match(regex, alert.key) for regex in KNOWN_ALERTS[data_center])


def is_suspended_coinbaseder(container_name: str, now: datetime) -> bool:
    if bool(re.match(r".*-coinbaseder-.*", container_name)) and (now.weekday() in {5, 6}):
        return True
    return False


def is_suspended_cme(container_name: str, now: datetime) -> bool:
    if bool(re.match(CME_CONTAINERS_RE, container_name)) and (
        (now.weekday() == 4 and now.hour >= 21) or now.weekday() == 5 or (now.weekday() == 6 and now.hour <= 21)
    ):
        return True
    if "erisx" in container_name and 21 <= now.hour <= 22:
        return True
    if "erisx" in container_name and (
        (now.weekday() == 4 and now.hour >= 21) or now.weekday() == 5 or (now.weekday() == 6 and now.hour <= 22)
    ):
        return True

    return False


def is_suspended_due_to_schedule(alert: Alert, date_point: datetime) -> bool:
    return any([is_suspended_cme(alert.container_name, date_point), is_suspended_coinbaseder(alert.container_name, date_point)])
