import logging
from argparse import ArgumentParser, Namespace
from collections import defaultdict
from logging.config import dictConfig
from os import makedirs
from os.path import dirname, join
from typing import Any, DefaultDict, Dict, Iterable, List, Optional, Tuple

import yaml

from src.octopus.generators.deployment.configs_generator import (
    DockerNetwork,
    ansible_content,
    generate_data_loaders_docker_compose,
    generate_db_stat_collector_docker_compose,
    generate_prometheus_targets_content,
    generate_prometheus_targets_content_for_data_loaders,
    generate_scrapers_docker_compose,
)
from src.octopus.generators.deployment.deployment_comparison_tool import compare_docker_compose_files, compare_prometheus_configs
from src.octopus.generators.deployment.yaml_dumper import IndentedYamlDumper
from src.octopus.inventory import cp1, production, cdev1
from src.octopus.inventory.types import (
    CollectionMode,
    DataLoader,
    DataType,
    DbStatCollector,
    DeploymentType,
    ExtraDirDeployment,
    FHGroup,
    MarketType,
    Scraper,
)

OVERCROWDED_NETWORK_THRESHOLD = 106

SCRAPERS_MAPPINGS: Dict[DataType, Any] = {
    DataType.TRADE: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_TRADE,
            DeploymentType.CDEV1: cdev1.REALTIME_TRADE,
            DeploymentType.CP1: cp1.REALTIME_TRADE,
        },
        CollectionMode.HISTORY: {
            DeploymentType.PROD: production.HISTORICAL_TRADE,
            DeploymentType.CDEV1: cdev1.HISTORICAL_TRADE,
            DeploymentType.CP1: cp1.HISTORICAL_TRADE,
        },
    },
    DataType.BOOK: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_BOOK,
            DeploymentType.CDEV1: cdev1.REALTIME_BOOK,
            DeploymentType.CP1: cp1.REALTIME_BOOK,
        },
        CollectionMode.HOURLY: {
            DeploymentType.PROD: production.HOURLY_BOOK,
            DeploymentType.CDEV1: cdev1.HOURLY_BOOK,
            DeploymentType.CP1: cp1.HOURLY_BOOK,
        },
    },
    DataType.OPEN_INTEREST: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_OPEN_INTEREST,
            DeploymentType.CDEV1: cdev1.REALTIME_OPEN_INTEREST,
            DeploymentType.CP1: cp1.REALTIME_OPEN_INTEREST,
        },
        CollectionMode.HISTORY: {
            DeploymentType.PROD: production.HISTORICAL_OPEN_INTEREST,
            DeploymentType.CDEV1: cdev1.HISTORICAL_OPEN_INTEREST,
            DeploymentType.CP1: cp1.HISTORICAL_OPEN_INTEREST,
        },
    },
    DataType.LIQUIDATION: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_LIQUIDATION,
            DeploymentType.CDEV1: cdev1.REALTIME_LIQUIDATION,
            DeploymentType.CP1: cp1.REALTIME_LIQUIDATION,
        },
        CollectionMode.HISTORY: {
            DeploymentType.PROD: production.HISTORICAL_LIQUIDATION,
            DeploymentType.CDEV1: cdev1.HISTORICAL_LIQUIDATION,
            DeploymentType.CP1: cp1.HISTORICAL_LIQUIDATION,
        },
    },
    DataType.FUNDING_RATE: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_FUNDING_RATE,
            DeploymentType.CDEV1: cdev1.REALTIME_FUNDING_RATE,
            DeploymentType.CP1: cp1.REALTIME_FUNDING_RATE,
        },
        CollectionMode.HISTORY: {
            DeploymentType.PROD: production.HISTORICAL_FUNDING_RATE,
            DeploymentType.CDEV1: cdev1.HISTORICAL_FUNDING_RATE,
            DeploymentType.CP1: cp1.HISTORICAL_FUNDING_RATE,
        },
    },
    DataType.QUOTE: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_QUOTES,
            DeploymentType.CDEV1: cdev1.REALTIME_QUOTES,
            DeploymentType.CP1: cp1.REALTIME_QUOTES,
        },
    },
    DataType.METADATA: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_METADATA,
            DeploymentType.CDEV1: cdev1.REALTIME_METADATA,
            DeploymentType.CP1: cp1.REALTIME_METADATA,
        },
    },
    DataType.TICKER_F: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_FUTURES_TICKER,
            DeploymentType.CDEV1: cdev1.REALTIME_FUTURES_TICKER,
            DeploymentType.CP1: cp1.REALTIME_FUTURES_TICKER,
        }
    },
    DataType.TICKER_O: {
        CollectionMode.REALTIME: {
            DeploymentType.PROD: production.REALTIME_OPTION_TICKER,
            DeploymentType.CDEV1: cdev1.REALTIME_OPTION_TICKER,
            DeploymentType.CP1: cp1.REALTIME_OPTION_TICKER,
        },
    },
}
DATA_LOADERS: Dict[DataType, Any] = {
    DataType.TRADE: {
        DeploymentType.PROD: production.TRADES_LOADERS,
        DeploymentType.CDEV1: cdev1.TRADES_LOADERS,
        DeploymentType.CP1: cp1.TRADES_LOADERS,
    },
    DataType.FUNDING_RATE: {
        DeploymentType.PROD: production.FUNDING_RATES_LOADERS,
        DeploymentType.CDEV1: cdev1.FUNDING_RATES_LOADERS,
        DeploymentType.CP1: cp1.FUNDING_RATES_LOADERS,
    },
    DataType.OPEN_INTEREST: {
        DeploymentType.PROD: production.OPEN_INTERESTS_LOADERS,
        DeploymentType.CDEV1: cdev1.OPEN_INTERESTS_LOADERS,
        DeploymentType.CP1: cp1.OPEN_INTERESTS_LOADERS,
    },
    DataType.LIQUIDATION: {
        DeploymentType.PROD: production.LIQUIDATIONS_LOADERS,
        DeploymentType.CDEV1: cdev1.LIQUIDATIONS_LOADERS,
        DeploymentType.CP1: cp1.LIQUIDATIONS_LOADERS,
    },
}
DB_STAT_COLLECTORS: Dict[DataType, Any] = {
    # DataType.TRADE: {
    #     DeploymentType.PROD: production.TRADES_DB_STAT_COLLECTORS,
    #     DeploymentType.CDEV1: cdev1.TRADES_DB_STAT_COLLECTORS,
    #     DeploymentType.CP1: cp1.TRADES_DB_STAT_COLLECTORS
    # }
}

EXTRA_DIRECTORIES: List[ExtraDirDeployment] = [
    ExtraDirDeployment(directory="production_metrics_daily"),
    ExtraDirDeployment(
        directory="production_proxy_check", command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}"
    ),
    ExtraDirDeployment(
        directory="production_daily_totals_trade_spot",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-daily-totals-trade-spot-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_daily_totals_trade_futures",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-daily-totals-trade-futures-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_daily_totals_trade_option",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-daily-totals-trade-option-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_daily_totals_book_spot",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-daily-totals-book-spot-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_daily_totals_book_futures",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-daily-totals-book-futures-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_daily_totals_book_option",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-daily-totals-book-option-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_historical_patch_trade_spot",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-historical-patch-trade-spot-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_historical_patch_trade_futures",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-historical-patch-trade-futures-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_historical_patch_trade_option",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-historical-patch-trade-option-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_historical_patch_book_spot",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-historical-patch-book-spot-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_historical_patch_book_futures",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-historical-patch-book-futures-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_historical_patch_book_option",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-historical-patch-book-option-scrapers-production-1:8080"]},
        ],
    ),
    ExtraDirDeployment(
        directory="production_historical_patch_option_ticker",
        command="docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        prometheus_targets=[
            {"targets": ["prod-historical-patch-option-ticker-scrapers-production-1:8080"]},
        ],
    ),
]

DEPLOYMENT_FILES_ROOT = join(dirname(__file__), "deploy")


def configure_logging() -> None:
    dictConfig({
        "version": 1,
        "formatters": {
            "default": {
                "class": "logging.Formatter",
                "format": "%(asctime)-15s %(levelname)s %(message)s",
                "datefmt": "[%Y-%m-%d %H:%M:%S]",
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "default",
            },
        },
        "loggers": {"": {"level": "DEBUG", "handlers": ["console"]}},
    })


def get_deployment_directory_name(
    deployment_part: str,
    data_type: DataType,
    collection_mode: CollectionMode,
    market_type: MarketType,
    group: Optional[FHGroup] = None,
) -> str:
    name = f"{deployment_part}_{data_type.m_name}_{collection_mode.m_name}_{market_type.m_name}"
    if group is not None:
        name += f"_{group.value}"
    return name


def get_data_loader_deployment_directory_name(deployment: DeploymentType, data_type: DataType) -> str:
    deployment_part = "production" if deployment == DeploymentType.PROD else deployment.m_name
    return f"{deployment_part}_{data_type.m_name}_loader"


def get_db_stat_collector_deployment_directory_name(deployment: DeploymentType, data_type: DataType) -> str:
    deployment_part = "production" if deployment == DeploymentType.PROD else deployment.m_name
    return f"{deployment_part}_{data_type.m_name}_db_stat_collector"


def generate_deployment() -> None:
    args = parse_cli()

    deployment_files_root = DEPLOYMENT_FILES_ROOT
    prometheus_configs_root = join(args.operations_root, "roles", "prometheus", "files", "discovery", "scrapers")
    networks_counter: DefaultDict[DockerNetwork, int] = defaultdict(int)

    if args.verify:
        deployment_files_root += "_new"
        prometheus_configs_root += "_new"

    logging.info("generating deployment configs into: %s", deployment_files_root)

    if args.operations_root:
        logging.info("generating prometheus configs into: %s", prometheus_configs_root)
        makedirs(prometheus_configs_root, exist_ok=True)

    for deployment in [DeploymentType.PROD]:
        for data_type, collection_mode, market_type, scrapers_configs in configurations_iterator(deployment):
            fh_groups: List[Optional[FHGroup]] = [None]
            deployment_part = deployment.m_name
            if deployment == DeploymentType.PROD:
                deployment_part = "production"
            if data_type == DataType.BOOK and collection_mode == CollectionMode.REALTIME and deployment == DeploymentType.PROD:
                if market_type in [MarketType.SPOT, MarketType.FUTURES]:
                    fh_groups = [FHGroup.GROUP_1, FHGroup.GROUP_2, FHGroup.GROUP_3, FHGroup.GROUP_4]
                else:  # MarketType.OPTION
                    fh_groups = [FHGroup.GROUP_1]
                fhs_with_empty_group = list(filter(lambda scraper: scraper.group is None, scrapers_configs))
                if fhs_with_empty_group:
                    raise ValueError(f"Following feed handlers must have a group: {fhs_with_empty_group}")
            for fh_group in fh_groups:
                scrapers_to_deploy = list(filter(lambda scraper: scraper.group == fh_group, scrapers_configs))
                directory_name = get_deployment_directory_name(
                    deployment_part, data_type, collection_mode, market_type, fh_group
                )
                makedirs(join(deployment_files_root, directory_name), exist_ok=True)

                docker_compose_file = generate_scrapers_docker_compose(
                    deployment, data_type, collection_mode, market_type, scrapers_to_deploy, fh_group, networks_counter
                )
                with open(join(deployment_files_root, directory_name, "docker-compose.yml"), "w") as yaml_file:
                    yaml_file.write(docker_compose_file + "\n")

                if args.verify:
                    compare_docker_compose_files(join(DEPLOYMENT_FILES_ROOT, directory_name), DEPLOYMENT_FILES_ROOT)

                ansible_yml = ansible_content(directory_name)
                with open(join(deployment_files_root, f"{directory_name}.ansible.yml"), "w") as ansible_file:
                    ansible_file.write(ansible_yml + "\n")

                if args.operations_root:
                    prometheus_targets_content = generate_prometheus_targets_content(
                        data_type, deployment, scrapers_to_deploy, fh_group
                    )
                    prometheus_targets_file_path = join(prometheus_configs_root, f"{directory_name}.yml")
                    with open(prometheus_targets_file_path, "w") as prometheus_targets_file:
                        prometheus_targets_file.write(prometheus_targets_content + "\n")

                    if args.verify:
                        compare_prometheus_configs(prometheus_targets_file_path, prometheus_configs_root)

        for data_type, data_loaders in data_loaders_iterator(deployment):
            if not data_loaders:
                continue
            directory_name = get_data_loader_deployment_directory_name(deployment, data_type)
            makedirs(join(deployment_files_root, directory_name), exist_ok=True)

            docker_compose_file = generate_data_loaders_docker_compose(deployment, data_type, data_loaders, networks_counter)
            with open(join(deployment_files_root, directory_name, "docker-compose.yml"), "w") as yaml_file:
                yaml_file.write(docker_compose_file + "\n")

            if args.verify:
                compare_docker_compose_files(join(DEPLOYMENT_FILES_ROOT, directory_name), DEPLOYMENT_FILES_ROOT)

            ansible_yml = ansible_content(directory_name)
            with open(join(deployment_files_root, f"{directory_name}.ansible.yml"), "w") as ansible_file:
                ansible_file.write(ansible_yml + "\n")

            if args.operations_root:
                prometheus_targets_content = generate_prometheus_targets_content_for_data_loaders(
                    data_type, deployment, data_loaders
                )
                prometheus_targets_file_path = join(prometheus_configs_root, f"{directory_name}.yml")
                with open(prometheus_targets_file_path, "w") as prometheus_targets_file:
                    prometheus_targets_file.write(prometheus_targets_content + "\n")

                if args.verify:
                    compare_prometheus_configs(prometheus_targets_file_path, prometheus_configs_root)

        for data_type, db_stat_collectors in db_stat_collectors_iterator(deployment):
            if not db_stat_collectors:
                continue
            directory_name = get_db_stat_collector_deployment_directory_name(deployment, data_type)
            makedirs(join(deployment_files_root, directory_name), exist_ok=True)

            docker_compose_file = generate_db_stat_collector_docker_compose(
                deployment, data_type, db_stat_collectors, networks_counter
            )
            with open(join(deployment_files_root, directory_name, "docker-compose.yml"), "w") as yaml_file:
                yaml_file.write(docker_compose_file + "\n")

            if args.verify:
                compare_docker_compose_files(join(DEPLOYMENT_FILES_ROOT, directory_name), DEPLOYMENT_FILES_ROOT)

            ansible_yml = ansible_content(directory_name)
            with open(join(deployment_files_root, f"{directory_name}.ansible.yml"), "w") as ansible_file:
                ansible_file.write(ansible_yml + "\n")

            if args.operations_root:
                prometheus_targets_content = generate_prometheus_targets_content_for_data_loaders(
                    data_type, deployment, data_loaders
                )
                prometheus_targets_file_path = join(prometheus_configs_root, f"{directory_name}.yml")
                with open(prometheus_targets_file_path, "w") as prometheus_targets_file:
                    prometheus_targets_file.write(prometheus_targets_content + "\n")

                if args.verify:
                    compare_prometheus_configs(prometheus_targets_file_path, prometheus_configs_root)

    extra_directories = [
        ("production_metrics_daily", None),
        ("production_proxy_check", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        ("production_daily_totals_trade_spot", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        ("production_daily_totals_trade_option", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        ("production_daily_totals_trade_futures", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        ("production_daily_totals_book_spot", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        ("production_daily_totals_book_futures", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        ("production_daily_totals_book_option", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        ("production_historical_patch_book_spot", "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"),
        (
            "production_historical_patch_book_futures",
            "docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        ),
        (
            "production_historical_patch_book_option",
            "docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        ),
        (
            "production_historical_patch_option_ticker",
            "docker-compose down --remove-orphans && docker-compose up -d {{ service }}",
        ),
    ]
    for directory_name, command in extra_directories:
        ansible_yml = ansible_content(directory_name, command)
        with open(join(deployment_files_root, f"{directory_name}.ansible.yml"), "w") as ansible_file:
            ansible_file.write(ansible_yml + "\n")

    generate_extra_directories_deployment(operations_root=args.operations_root)
    overcrowded_networks = sorted(
        list(
            filter(
                lambda network: network[0]
                not in [
                    DockerNetwork.KAFKA_BRIDGE,
                    DockerNetwork.LOCAL_PGBOUNCER,
                    DockerNetwork.STAGING3,
                    DockerNetwork.FHS_KAFKA_STAGING,
                    DockerNetwork.LPN_BOOK,
                    DockerNetwork.LPN_TRADE,
                ]
                and network[1] > OVERCROWDED_NETWORK_THRESHOLD,
                networks_counter.items(),
            )
        ),
        key=lambda network: network[1],
        reverse=True,
    )
    if overcrowded_networks:
        logging.warning(
            f"The following docker swarm networks have too many containers inside: {overcrowded_networks}. "
            f"Please consider moving them to the other networks to prevent potential network issues."
        )


def generate_extra_directories_deployment(operations_root: Optional[str] = None) -> None:
    for deployment in EXTRA_DIRECTORIES:
        ansible_yml = ansible_content(target_dir_name=deployment.directory, command=deployment.command)

        with open(join(DEPLOYMENT_FILES_ROOT, f"{deployment.directory}.ansible.yml"), "w") as ansible_file:
            ansible_file.write(ansible_yml + "\n")

        if operations_root and deployment.prometheus_targets:
            prometheus_configs_root = join(operations_root, "roles", "prometheus", "files", "discovery", "scrapers")
            prometheus_targets_content = str(yaml.dump(deployment.prometheus_targets, Dumper=IndentedYamlDumper))
            prometheus_targets_file_path = join(prometheus_configs_root, f"{deployment.directory}.yml")

            with open(prometheus_targets_file_path, "w") as prometheus_targets_file:
                prometheus_targets_file.write(prometheus_targets_content + "\n")


def parse_cli() -> Namespace:
    arg_parser = ArgumentParser()
    arg_parser.add_argument(
        "--verify", action="store_true", default=False, help="verify the result deployment files by comparing to existing ones"
    )

    arg_parser.add_argument("--operations-root", type=str, default="", help="path to operations folder root")

    return arg_parser.parse_args()


ConfigCollectionType = Tuple[DataType, CollectionMode, MarketType, List[Scraper]]


def configurations_iterator(deployment: DeploymentType) -> Iterable[ConfigCollectionType]:
    for data_type, scrapers_per_collection in SCRAPERS_MAPPINGS.items():
        for collection_mode, scrapers_configs in scrapers_per_collection.items():
            for market_type in MarketType:
                market_scraper_configs = [
                    scraper for scraper in scrapers_configs[deployment] if scraper.market_type == market_type
                ]
                if len(market_scraper_configs) > 0:
                    yield data_type, collection_mode, market_type, market_scraper_configs


def data_loaders_iterator(deployment: DeploymentType) -> Iterable[Tuple[DataType, List[DataLoader]]]:
    for data_type, data_loaders in DATA_LOADERS.items():
        yield data_type, data_loaders.get(deployment) or []


def db_stat_collectors_iterator(deployment: DeploymentType) -> Iterable[Tuple[DataType, List[DbStatCollector]]]:
    for data_type, db_stat_collectors in DB_STAT_COLLECTORS.items():
        yield data_type, db_stat_collectors.get(deployment) or []


if __name__ == "__main__":
    configure_logging()
    generate_deployment()
