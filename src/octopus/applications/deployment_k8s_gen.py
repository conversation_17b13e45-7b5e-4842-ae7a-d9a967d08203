import glob
import logging
import os
from copy import deepcopy
from os import makedirs
from os.path import dirname, join
from typing import Dict, List, Optional, Set, Tuple

from src.octopus.applications.deployment_configs_gen import (
    configurations_iterator,
    configure_logging,
    data_loaders_iterator,
    db_stat_collectors_iterator,
)
from src.octopus.generators.deployment.data_loader_types.defaults import DATA_LOADER_DEFAULTS
from src.octopus.generators.deployment.db_stat_collector_types.default import DB_STAT_COLLECTOR_DEFAULTS
from src.octopus.generators.deployment.scraper_types.defaults import SCRAPER_DEFAULTS
from src.octopus.generators.k8s_gen import (
    generate_data_loader_script,
    generate_db_stat_collector_script,
    generate_script,
    generate_shipper_script,
    get_master_deploy_script_name,
    write_gitlab_yaml_file,
    write_master_deploy_scripts,
)
from src.octopus.inventory.types import CollectionMode, ConnectionMode, DataType, DeploymentType, FHGroup, MarketType

DEPLOYMENT_FILES_ROOT = join(dirname(__file__), "deploy_k8s")
YAML_FILES_FOLDER = "deploy-values"

_SHIPPER_LIST = [
    (MarketType.FUTURES, DataType.BOOK),
    (MarketType.FUTURES, DataType.FUNDING_RATE),
    (MarketType.FUTURES, DataType.LIQUIDATION),
    (MarketType.FUTURES, DataType.OPEN_INTEREST),
    (MarketType.OPTION, DataType.OPEN_INTEREST),
    (MarketType.SPOT, DataType.BOOK),
    (MarketType.OPTION, DataType.TICKER_O),
]

_EXCHANGE_SHIPPER_LIST = [
    (MarketType.FUTURES, DataType.TICKER_F),
    (MarketType.FUTURES, DataType.TRADE),
    (MarketType.OPTION, DataType.TRADE),
    (MarketType.OPTION, DataType.TRADE_EXTRA),
    (MarketType.SPOT, DataType.TRADE),
]

_EXCHANGE_SHIPPER_EXCLUDE_LIST: Tuple[str, ConnectionMode, MarketType] = [
    ("BinanceAgg", ConnectionMode.STREAMING, MarketType.FUTURES)
]


_CUSTOM_DEPLOYMENTS = {
    "fh-monitoring.sh": ["fh-monitoring"],
    "fh-alerting.sh": ["fh-alerting"],
    "metrics-daily-feed-handler.sh": ["metrics-daily-feed-handler"],
    "proxy-check.sh": ["proxy-check"],
    "daily-totals.sh": [
        "daily-totals-trad-spot",
        "daily-totals-trad-futu",
        "daily-totals-trad-opti",
        "daily-totals-liqu-futu",
        "daily-totals-open-futu",
        "daily-totals-open-opti",
        "daily-totals-fund-futu",
        "daily-totals-tick-futu",
        "daily-totals-tick-opti",
        "daily-totals-book-spot",
        "daily-totals-book-futu",
        "daily-totals-book-opti",
    ],
    "historical-patch.sh": [
        "historical-patch-trad-spot",
        "historical-patch-trad-futu",
        "historical-patch-trad-opti",
        "historical-patch-open-futu",
        "historical-patch-liqu-futu",
        "historical-patch-fund-futu",
        "historical-patch-open-opti",
        "historical-patch-tick-futu",
        "historical-patch-tick-opti",
        "historical-patch-book-spot",
        "historical-patch-book-futu",
        "historical-patch-book-opti",
    ],
    "patch-job.sh": ["patch-job"],
}


def should_generate_shipper(deployment: DeploymentType) -> bool:
    return True


def generate_deployment() -> None:
    for deployment in [DeploymentType.CDEV1, DeploymentType.CP1]:
        master_deploy: Dict[str, List[str]] = deepcopy(_CUSTOM_DEPLOYMENTS)
        deploy_yaml_list: List[str] = []
        shipper_exchanges: Dict[Tuple[MarketType, DataType], Set[str]] = {x: set() for x in _EXCHANGE_SHIPPER_LIST}

        deployment_files_root = os.path.join(DEPLOYMENT_FILES_ROOT, deployment.m_name)
        yaml_files_root = os.path.join(deployment_files_root, YAML_FILES_FOLDER)
        makedirs(deployment_files_root, exist_ok=True)
        makedirs(yaml_files_root, exist_ok=True)

        logging.info("generating deployment scripts into: %s", deployment_files_root)
        logging.info("generating deployment value files into: %s", yaml_files_root)

        for data_type, collection_mode, market_type, scrapers_configs in configurations_iterator(deployment):
            fh_groups: List[Optional[FHGroup]] = [None]
            if data_type == DataType.BOOK and collection_mode == CollectionMode.REALTIME and deployment in (DeploymentType.CP1,):
                if market_type in [MarketType.SPOT, MarketType.FUTURES]:
                    fh_groups = [FHGroup.GROUP_1, FHGroup.GROUP_2, FHGroup.GROUP_3, FHGroup.GROUP_4]
                else:  # MarketType.OPTION
                    fh_groups = [FHGroup.GROUP_1]
                fhs_with_empty_group = list(filter(lambda scraper: scraper.group is None, scrapers_configs))
                if fhs_with_empty_group:
                    raise ValueError(f"Following feed handlers must have a group: {fhs_with_empty_group}")
            for fh_group in fh_groups:
                master_script_name = get_master_deploy_script_name(data_type, collection_mode, market_type, fh_group)
                if master_script_name not in master_deploy:
                    master_deploy[master_script_name] = []
                for config in filter(lambda scraper: scraper.group == fh_group, scrapers_configs):
                    deployment_type_dict = SCRAPER_DEFAULTS[data_type][deployment][config.connection_mode]
                    if isinstance(deployment_type_dict, dict):
                        scraper_deployment_constructor = deployment_type_dict[collection_mode]
                    else:
                        scraper_deployment_constructor = deployment_type_dict
                    service = scraper_deployment_constructor(config)

                    if (market_type, data_type) in shipper_exchanges:
                        if (
                            config.exchange_name,
                            config.connection_mode,
                            config.market_type,
                        ) not in _EXCHANGE_SHIPPER_EXCLUDE_LIST:
                            shipper_exchanges[(market_type, data_type)].add(config.exchange_name.split()[0])

                    content = generate_script(config, data_type, collection_mode, service, deployment)
                    pod_name = config.get_pod_prefix(data_type)
                    filename = f"{pod_name}-values.yaml"
                    with open(f"{yaml_files_root}/{filename}", "w") as fh:
                        fh.write(content)
                    deploy_yaml_list.append(filename)
                    master_deploy[master_script_name].append(pod_name)

        for data_type, data_loaders in data_loaders_iterator(deployment):
            for data_loader in data_loaders:
                defaults_config = DATA_LOADER_DEFAULTS[data_type][deployment]
                deployment_config = defaults_config(data_loader)

                master_script_name = f"deploy-{data_type.m_name}-data-loaders.sh".replace("_", "-")
                if master_script_name not in master_deploy:
                    master_deploy[master_script_name] = []

                content = generate_data_loader_script(
                    data_loader, data_type=data_type, deployment=deployment_config, deployment_type=deployment
                )
                pod_name = data_loader.get_container_prefix(data_type)
                filename = f"{pod_name}-values.yaml"
                with open(f"{yaml_files_root}/{filename}", "w") as fh:
                    fh.write(content)
                deploy_yaml_list.append(filename)
                master_deploy[master_script_name].append(pod_name)

        for data_type, db_stat_collectors in db_stat_collectors_iterator(deployment):
            for db_stat_collector in db_stat_collectors:
                defaults_config = DB_STAT_COLLECTOR_DEFAULTS[data_type][deployment]
                deployment_config = defaults_config(db_stat_collector)

                master_script_name = f"deploy-{data_type.m_name}-stat-collector.sh".replace("_", "-")
                if master_script_name not in master_deploy:
                    master_deploy[master_script_name] = []

                content = generate_db_stat_collector_script(
                    db_stat_collector, data_type=data_type, deployment=deployment_config, deployment_type=deployment
                )
                pod_name = db_stat_collector.get_container_prefix(data_type)
                filename = f"{pod_name}-values.yaml"
                with open(f"{yaml_files_root}/{filename}", "w") as fh:
                    fh.write(content)
                deploy_yaml_list.append(filename)
                master_deploy[master_script_name].append(pod_name)

        if should_generate_shipper(deployment):
            # Configure non-sharded table shippers
            for market_type, data_type in _SHIPPER_LIST:
                pod_prefix = f"shipper-{market_type.m_name}-{data_type.m_name}".replace("_", "-")
                content = generate_shipper_script(deployment, pod_prefix, market_type, data_type, set())
                filename = f"{pod_prefix}-values.yaml"
                with open(f"{yaml_files_root}/{filename}", "w") as fh:
                    fh.write(content)
                deploy_yaml_list.append(filename)
                fh_group = FHGroup.GROUP_1 if data_type == DataType.BOOK and deployment == DeploymentType.CP1 else None
                master_script_name = get_master_deploy_script_name(data_type, CollectionMode.REALTIME, market_type, fh_group)
                if master_script_name in master_deploy:
                    # Don't generate shippers if there are no feed handlers
                    master_deploy[master_script_name].append(pod_prefix)
            # Configure sharded table shippers
            for market_type, data_type in shipper_exchanges:
                exchange_list = shipper_exchanges[(market_type, data_type)]
                pod_prefix = f"shipper-{market_type.m_name}-{data_type.m_name}-exch".replace("_", "-")
                content = generate_shipper_script(deployment, pod_prefix, market_type, data_type, exchange_list)
                filename = f"{pod_prefix}-values.yaml"
                with open(f"{yaml_files_root}/{filename}", "w") as fh:
                    fh.write(content)
                deploy_yaml_list.append(filename)
                master_script_name = get_master_deploy_script_name(data_type, CollectionMode.REALTIME, market_type)
                if master_script_name not in master_deploy:
                    master_deploy[master_script_name] = []
                master_deploy[master_script_name].append(pod_prefix)

        write_master_deploy_scripts(deployment_files_root, master_deploy)

        # Remove any files that were not generated as part of this script (i.e., delete orphans)
        yaml_files = glob.glob(f"{yaml_files_root}/feed-handler-*-values.yaml")
        yaml_files += glob.glob(f"{yaml_files_root}/shipper-*-values.yaml")
        deploy_scripts = glob.glob(f"{deployment_files_root}/*.sh")
        for full_path in yaml_files + deploy_scripts:
            filename = os.path.basename(full_path)
            if filename not in master_deploy and filename not in deploy_yaml_list and "secret" not in filename:
                logging.warning(f"Removing deployment file: {full_path}")
                os.unlink(full_path)

        # Finally, update the .gitlab-ci.yaml file
        all_deploy_scripts = glob.glob(f"{deployment_files_root}/*.sh")
        write_gitlab_yaml_file(deployment, deployment_files_root, all_deploy_scripts)


if __name__ == "__main__":
    configure_logging()
    generate_deployment()
