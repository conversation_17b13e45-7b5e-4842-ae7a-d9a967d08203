from collections import defaultdict
from copy import deepcopy
from typing import Dict, Iterable, Tu<PERSON>, Set

from src.octopus.applications.deployment_configs_gen import SCRAPERS_MAPPINGS
from src.octopus.arguments import get_proxies_total
from src.octopus.generators.deployment.scraper_types.defaults import SCRAPER_DEFAULTS
from src.octopus.generators.deployment.scraper_types.general import ScraperDeployment
from src.octopus.generators.utils import update_obj_attributes_recursively
from src.octopus.inventory.types import DeploymentType, DataType, CollectionMode
from src.utils.http import ProxyParams


class ProxyAdmin:
    def __init__(self):
        self.proxies = []
        self.proxies_per_dc = defaultdict(set)
        self.proxies_per_fh = defaultdict(set)

        self._init_proxy_maps()

    @property
    def map(self):
        return self.proxies_per_dc

    def _init_proxy_maps(self) -> None:
        for data_center, data_type, collection_mode, fh_deployment in self.iterate_deployments():
            fh_name = fh_deployment.scraper.get_instance(data_center, data_type)
            fh_proxies = self.fh_proxies(fh_deployment)
            fh_proxy_groups = self.fh_proxy_groups(fh_deployment)

            self.proxies.append((fh_name, data_center, data_type, collection_mode, fh_proxies, fh_proxy_groups))
            self.proxies_per_dc[data_center].update(fh_proxies)
            self.proxies_per_fh[fh_name] = fh_proxies

    def get_proxies_per_vendor(self, data_center: DeploymentType) -> Dict[str, int]:
        return {
            "Smartproxy": len(list(proxy for proxy in self.proxies_per_dc.get(data_center, []) if self.is_smartproxy(proxy))),
            "WebShare": len(list(proxy for proxy in self.proxies_per_dc.get(data_center, []) if self.is_webshare(proxy))),
            "SelfHosted": len(list(proxy for proxy in self.proxies_per_dc.get(data_center, []) if self.is_self_hosted(proxy))),
        }

    @staticmethod
    def iterate_deployments() -> Iterable[Tuple[DeploymentType, DataType, CollectionMode, ScraperDeployment]]:
        for data_type in SCRAPERS_MAPPINGS:
            for collection_mode in SCRAPERS_MAPPINGS[data_type]:
                for data_center in SCRAPERS_MAPPINGS[data_type][collection_mode]:
                    for fh in SCRAPERS_MAPPINGS[data_type][collection_mode][data_center]:
                        fh_default_args = SCRAPER_DEFAULTS[data_type][data_center][fh.connection_mode]
                        if isinstance(fh_default_args, dict):
                            fh_deployment_constructor = fh_default_args[collection_mode]
                        else:
                            fh_deployment_constructor = fh_default_args
                        fh_deployment = fh_deployment_constructor(fh)

                        update_obj_attributes_recursively(
                            fh_deployment, deepcopy(fh.deployment_settings), raise_error_on_missing_attributes=True
                        )
                        yield data_center, data_type, collection_mode, fh_deployment

    @staticmethod
    def fh_proxies(fh_deployment: ScraperDeployment) -> Set[str]:
        args = fh_deployment.entrypoint_args
        prioritized_proxies = {
            *get_proxies_total(
                [ProxyParams.from_string(proxy) for proxy in args.proxies] if args.proxies else [],
                args.proxy_groups or [],
                args.exclude_proxies or [],
                instance_number=1,
            ),
            *get_proxies_total(
                [ProxyParams.from_string(proxy) for proxy in args.proxies] if args.proxies else [],
                args.proxy_groups or [],
                args.exclude_proxies or [],
                instance_number=2,
            ),
        }
        return {str(prioritized_proxy[1]) for prioritized_proxy in prioritized_proxies}

    @staticmethod
    def fh_proxy_groups(fh_deployment: ScraperDeployment) -> Set[str]:
        args = fh_deployment.entrypoint_args
        fh_proxy_groups = set()
        for proxy_groups_in_tier in args.proxy_groups or [[]]:
            for proxy_group in proxy_groups_in_tier:
                if proxy_group is not None:
                    fh_proxy_groups.add(proxy_group.split("[")[0])
        return fh_proxy_groups

    @staticmethod
    def is_smartproxy(proxy: str) -> bool:
        return "CMSmartProxy" in proxy

    @staticmethod
    def is_webshare(proxy: str) -> bool:
        return "bgtruidt:pxwspzq7gr6k" in proxy

    @staticmethod
    def is_self_hosted(proxy: str) -> bool:
        return "seconduser" in proxy

    def print_proxies_per_dc(self) -> None:
        for dc in self.proxies_per_dc:
            print(f"Proxies for {dc.name}:", len(admin.map[dc]))
            for vendor, proxies_number in admin.get_proxies_per_vendor(dc).items():
                print(f"  {vendor}: {proxies_number}")


if __name__ == "__main__":
    admin = ProxyAdmin()
    admin.print_proxies_per_dc()

    while True:
        # TODO: add management commands here
        command = input()
