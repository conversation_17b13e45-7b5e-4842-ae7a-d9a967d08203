version: "3.7"

services:
  binance-quot-spot-stre-0000-0050:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0000-0050-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0000:0050
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[1/29],binance-ny5-hetzner-eu-quote-realtime[1/29],binance-prod-hetzner-eu-book-tick-by-tick[1/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0050-0100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0050-0100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0050:0100
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[2/29],binance-ny5-hetzner-eu-quote-realtime[2/29],binance-prod-hetzner-eu-book-tick-by-tick[2/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0100-0150:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0100-0150-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0100:0150
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[3/29],binance-ny5-hetzner-eu-quote-realtime[3/29],binance-prod-hetzner-eu-book-tick-by-tick[3/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0150-0200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0150-0200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0150:0200
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[4/29],binance-ny5-hetzner-eu-quote-realtime[4/29],binance-prod-hetzner-eu-book-tick-by-tick[4/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0200-0250:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0200-0250-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0200:0250
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[5/29],binance-ny5-hetzner-eu-quote-realtime[5/29],binance-prod-hetzner-eu-book-tick-by-tick[5/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0250-0300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0250-0300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0250:0300
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[6/29],binance-ny5-hetzner-eu-quote-realtime[6/29],binance-prod-hetzner-eu-book-tick-by-tick[6/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0300-0350:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0300-0350-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0300:0350
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[7/29],binance-ny5-hetzner-eu-quote-realtime[7/29],binance-prod-hetzner-eu-book-tick-by-tick[7/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0350-0400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0350-0400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0350:0400
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[8/29],binance-ny5-hetzner-eu-quote-realtime[8/29],binance-prod-hetzner-eu-book-tick-by-tick[8/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0400-0450:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0400-0450-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0400:0450
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[9/29],binance-ny5-hetzner-eu-quote-realtime[9/29],binance-prod-hetzner-eu-book-tick-by-tick[9/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0450-0500:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0450-0500-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0450:0500
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[10/29],binance-ny5-hetzner-eu-quote-realtime[10/29],binance-prod-hetzner-eu-book-tick-by-tick[10/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0500-0550:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0500-0550-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0500:0550
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[11/29],binance-ny5-hetzner-eu-quote-realtime[11/29],binance-prod-hetzner-eu-book-tick-by-tick[11/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0550-0600:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0550-0600-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0550:0600
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[12/29],binance-ny5-hetzner-eu-quote-realtime[12/29],binance-prod-hetzner-eu-book-tick-by-tick[12/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0600-0650:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0600-0650-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0600:0650
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[13/29],binance-ny5-hetzner-eu-quote-realtime[13/29],binance-prod-hetzner-eu-book-tick-by-tick[13/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0650-0700:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0650-0700-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0650:0700
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[14/29],binance-ny5-hetzner-eu-quote-realtime[14/29],binance-prod-hetzner-eu-book-tick-by-tick[14/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0700-0750:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0700-0750-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0700:0750
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[15/29],binance-ny5-hetzner-eu-quote-realtime[15/29],binance-prod-hetzner-eu-book-tick-by-tick[15/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0750-0800:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0750-0800-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0750:0800
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[16/29],binance-ny5-hetzner-eu-quote-realtime[16/29],binance-prod-hetzner-eu-book-tick-by-tick[16/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0800-0850:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0800-0850-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0800:0850
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[17/29],binance-ny5-hetzner-eu-quote-realtime[17/29],binance-prod-hetzner-eu-book-tick-by-tick[17/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0850-0900:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0850-0900-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0850:0900
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[18/29],binance-ny5-hetzner-eu-quote-realtime[18/29],binance-prod-hetzner-eu-book-tick-by-tick[18/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0900-0950:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0900-0950-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0900:0950
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[19/29],binance-ny5-hetzner-eu-quote-realtime[19/29],binance-prod-hetzner-eu-book-tick-by-tick[19/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-0950-1000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-0950-1000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 0950:1000
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[20/29],binance-ny5-hetzner-eu-quote-realtime[20/29],binance-prod-hetzner-eu-book-tick-by-tick[20/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1000-1050:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1000-1050-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1000:1050
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[21/29],binance-ny5-hetzner-eu-quote-realtime[21/29],binance-prod-hetzner-eu-book-tick-by-tick[21/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1050-1100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1050-1100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1050:1100
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[22/29],binance-ny5-hetzner-eu-quote-realtime[22/29],binance-prod-hetzner-eu-book-tick-by-tick[22/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1100-1150:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1100-1150-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1100:1150
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[23/29],binance-ny5-hetzner-eu-quote-realtime[23/29],binance-prod-hetzner-eu-book-tick-by-tick[23/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1150-1200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1150-1200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1150:1200
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[24/29],binance-ny5-hetzner-eu-quote-realtime[24/29],binance-prod-hetzner-eu-book-tick-by-tick[24/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1200-1250:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1200-1250-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1200:1250
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[25/29],binance-ny5-hetzner-eu-quote-realtime[25/29],binance-prod-hetzner-eu-book-tick-by-tick[25/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1250-1300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1250-1300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1250:1300
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[26/29],binance-ny5-hetzner-eu-quote-realtime[26/29],binance-prod-hetzner-eu-book-tick-by-tick[26/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1300-1350:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1300-1350-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1300:1350
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[27/29],binance-ny5-hetzner-eu-quote-realtime[27/29],binance-prod-hetzner-eu-book-tick-by-tick[27/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1350-1400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1350-1400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1350:1400
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[28/29],binance-ny5-hetzner-eu-quote-realtime[28/29],binance-prod-hetzner-eu-book-tick-by-tick[28/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binance-quot-spot-stre-1400-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-quot-spot-stre-1400-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_4:9GB
      --machine {{ inventory_hostname }}
      --market-range 1400:9000
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-quote-realtime[29/29],binance-ny5-hetzner-eu-quote-realtime[29/29],binance-prod-hetzner-eu-book-tick-by-tick[29/29]
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  binanceus-quot-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binanceus-quot-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Binance.US
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_35:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-quot-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-quot-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bitstamp
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_0:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 5
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-quot-spot-stre-000-100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-quot-spot-stre-000-100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bybit
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_42:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:100
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-quote-realtime[1/7],bybit-prod-webshare-eu-quote-realtime[1/7]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-quot-spot-stre-100-200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-quot-spot-stre-100-200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bybit
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_42:3GB
      --machine {{ inventory_hostname }}
      --market-range 100:200
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-quote-realtime[2/7],bybit-prod-webshare-eu-quote-realtime[2/7]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-quot-spot-stre-200-300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-quot-spot-stre-200-300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bybit
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_42:3GB
      --machine {{ inventory_hostname }}
      --market-range 200:300
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-quote-realtime[3/7],bybit-prod-webshare-eu-quote-realtime[3/7]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-quot-spot-stre-300-400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-quot-spot-stre-300-400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bybit
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_42:3GB
      --machine {{ inventory_hostname }}
      --market-range 300:400
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-quote-realtime[4/7],bybit-prod-webshare-eu-quote-realtime[4/7]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-quot-spot-stre-400-500:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-quot-spot-stre-400-500-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bybit
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_42:3GB
      --machine {{ inventory_hostname }}
      --market-range 400:500
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-quote-realtime[5/7],bybit-prod-webshare-eu-quote-realtime[5/7]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-quot-spot-stre-500-600:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-quot-spot-stre-500-600-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bybit
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_42:3GB
      --machine {{ inventory_hostname }}
      --market-range 500:600
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-quote-realtime[6/7],bybit-prod-webshare-eu-quote-realtime[6/7]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-quot-spot-stre-600-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-quot-spot-stre-600-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Bybit
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_42:3GB
      --machine {{ inventory_hostname }}
      --market-range 600:9000
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-quote-realtime[7/7],bybit-prod-webshare-eu-quote-realtime[7/7]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-btcusd-quot-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-btcusd-quot-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --instruments btc-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-ethusd-quot-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-ethusd-quot-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --instruments eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-000-050:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-000-050-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:050
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-050-100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-050-100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 050:100
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-100-150:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-100-150-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 100:150
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-150-200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-150-200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 150:200
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-200-250:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-200-250-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 200:250
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-250-300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-250-300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 250:300
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-300-350:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-300-350-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 300:350
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-quot-spot-stre-350-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-quot-spot-stre-350-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Coinbase
      --exclude-instruments btc-usd eth-usd
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_1:3GB
      --machine {{ inventory_hostname }}
      --market-range 350:9000
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-quot-spot-stre-000-060:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-quot-spot-stre-000-060-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Gemini
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_5:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:060
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-ny5-hetzner-us-quote-realtime[1/3],gemini-hetzner-eu-quote-realtime[1/3]
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-quot-spot-stre-060-120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-quot-spot-stre-060-120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Gemini
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_5:3GB
      --machine {{ inventory_hostname }}
      --market-range 060:120
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-ny5-hetzner-us-quote-realtime[2/3],gemini-hetzner-eu-quote-realtime[2/3]
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-quot-spot-stre-120-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-quot-spot-stre-120-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Gemini
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_5:3GB
      --machine {{ inventory_hostname }}
      --market-range 120:9000
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-ny5-hetzner-us-quote-realtime[3/3],gemini-hetzner-eu-quote-realtime[3/3]
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  hitbtc-quot-spot-stre-000-750:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-hitbtc-quot-spot-stre-000-750-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler HitBTC
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_11:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:750
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  hitbtc-quot-spot-stre-750-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-hitbtc-quot-spot-stre-750-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler HitBTC
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_11:3GB
      --machine {{ inventory_hostname }}
      --market-range 750:9000
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-000-110:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-000-110-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 000:110
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-110-220:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-110-220-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 110:220
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-220-330:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-220-330-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 220:330
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-330-440:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-330-440-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 330:440
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-440-550:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-440-550-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 440:550
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-550-660:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-550-660-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 550:660
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-660-770:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-660-770-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 660:770
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-770-880:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-770-880-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 770:880
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-quot-spot-stre-880-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-quot-spot-stre-880-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Huobi
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_10:9GB
      --machine {{ inventory_hostname }}
      --market-range 880:9000
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-quot-spot-stre-000-400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-quot-spot-stre-000-400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Kraken
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_6:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:400
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.1
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-quot-spot-stre-400-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-quot-spot-stre-400-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler Kraken
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_6:3GB
      --machine {{ inventory_hostname }}
      --market-range 400:9000
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.1
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  okex-quot-spot-stre-000-120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-okex-quot-spot-stre-000-120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler OKEx
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_9:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:120
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  okex-quot-spot-stre-120-240:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-okex-quot-spot-stre-120-240-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler OKEx
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_9:3GB
      --machine {{ inventory_hostname }}
      --market-range 120:240
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  okex-quot-spot-stre-240-360:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-okex-quot-spot-stre-240-360-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler OKEx
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_9:3GB
      --machine {{ inventory_hostname }}
      --market-range 240:360
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]

  okex-quot-spot-stre-360-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-okex-quot-spot-stre-360-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_quote_feed_handler OKEx
      --kafka-out
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes quotes_9:3GB
      --machine {{ inventory_hostname }}
      --market-range 360:9000
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books3, kafka_coinmetrics-kafka-zookeeper-local]


networks:
  coinmetrics-market-books3:
    external: true
    driver: overlay
  kafka_coinmetrics-kafka-zookeeper-local:
    external: true
