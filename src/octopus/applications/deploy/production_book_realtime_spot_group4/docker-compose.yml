version: "3.7"

services:
  binance-book-spot-stre-0000-0050:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0000-0050-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0000:0050
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[1/29],binance-ny5-hetzner-eu-book-realtime[1/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0050-0100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0050-0100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0050:0100
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[2/29],binance-ny5-hetzner-eu-book-realtime[2/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0100-0150:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0100-0150-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0100:0150
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[3/29],binance-ny5-hetzner-eu-book-realtime[3/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0150-0200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0150-0200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0150:0200
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[4/29],binance-ny5-hetzner-eu-book-realtime[4/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0200-0250:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0200-0250-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0200:0250
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[5/29],binance-ny5-hetzner-eu-book-realtime[5/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0250-0300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0250-0300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0250:0300
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[6/29],binance-ny5-hetzner-eu-book-realtime[6/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0300-0350:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0300-0350-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0300:0350
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[7/29],binance-ny5-hetzner-eu-book-realtime[7/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0350-0400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0350-0400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0350:0400
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[8/29],binance-ny5-hetzner-eu-book-realtime[8/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0400-0450:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0400-0450-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0400:0450
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[9/29],binance-ny5-hetzner-eu-book-realtime[9/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0450-0500:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0450-0500-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0450:0500
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[10/29],binance-ny5-hetzner-eu-book-realtime[10/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0500-0550:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0500-0550-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0500:0550
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[11/29],binance-ny5-hetzner-eu-book-realtime[11/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0550-0600:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0550-0600-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0550:0600
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[12/29],binance-ny5-hetzner-eu-book-realtime[12/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0600-0650:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0600-0650-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0600:0650
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[13/29],binance-ny5-hetzner-eu-book-realtime[13/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0650-0700:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0650-0700-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0650:0700
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[14/29],binance-ny5-hetzner-eu-book-realtime[14/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0700-0750:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0700-0750-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0700:0750
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[15/29],binance-ny5-hetzner-eu-book-realtime[15/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0750-0800:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0750-0800-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0750:0800
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[16/29],binance-ny5-hetzner-eu-book-realtime[16/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0800-0850:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0800-0850-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0800:0850
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[17/29],binance-ny5-hetzner-eu-book-realtime[17/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0850-0900:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0850-0900-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0850:0900
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[18/29],binance-ny5-hetzner-eu-book-realtime[18/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0900-0950:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0900-0950-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0900:0950
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[19/29],binance-ny5-hetzner-eu-book-realtime[19/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-0950-1000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-0950-1000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0950:1000
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[20/29],binance-ny5-hetzner-eu-book-realtime[20/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1000-1050:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1000-1050-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1000:1050
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[21/29],binance-ny5-hetzner-eu-book-realtime[21/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1050-1100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1050-1100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1050:1100
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[22/29],binance-ny5-hetzner-eu-book-realtime[22/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1100-1150:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1100-1150-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1100:1150
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[23/29],binance-ny5-hetzner-eu-book-realtime[23/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1150-1200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1150-1200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1150:1200
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[24/29],binance-ny5-hetzner-eu-book-realtime[24/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1200-1250:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1200-1250-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1200:1250
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[25/29],binance-ny5-hetzner-eu-book-realtime[25/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1250-1300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1250-1300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1250:1300
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[26/29],binance-ny5-hetzner-eu-book-realtime[26/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1300-1350:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1300-1350-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1300:1350
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[27/29],binance-ny5-hetzner-eu-book-realtime[27/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1350-1400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1350-1400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1350:1400
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[28/29],binance-ny5-hetzner-eu-book-realtime[28/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-book-spot-stre-1400-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-book-spot-stre-1400-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 1400:9000
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[29/29],binance-ny5-hetzner-eu-book-realtime[29/29]
      --rate-limit-multiplier 0.01
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-busy-book-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-busy-book-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binanceus-all-book-spot-stre-000-080:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binanceus-all-book-spot-stre-000-080-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance.US
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_35.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:080
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-tick-by-tick[1/3]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binanceus-all-book-spot-stre-080-160:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binanceus-all-book-spot-stre-080-160-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance.US
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_35.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 080:160
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-tick-by-tick[2/3]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binanceus-all-book-spot-stre-160-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binanceus-all-book-spot-stre-160-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance.US
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_35.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 160:9000
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-tick-by-tick[3/3]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-book-spot-stre-000-060:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-book-spot-stre-000-060-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bitstamp
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_0.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:060
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[1/5],hetzner-us-book-tick-by-tick[1/5]
      --rate-limit-multiplier 0.5
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books6, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-book-spot-stre-060-120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-book-spot-stre-060-120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bitstamp
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_0.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 060:120
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[2/5],hetzner-us-book-tick-by-tick[2/5]
      --rate-limit-multiplier 0.5
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books6, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-book-spot-stre-120-180:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-book-spot-stre-120-180-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bitstamp
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_0.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 120:180
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[3/5],hetzner-us-book-tick-by-tick[3/5]
      --rate-limit-multiplier 0.5
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books6, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-book-spot-stre-180-240:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-book-spot-stre-180-240-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bitstamp
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_0.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 180:240
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[4/5],hetzner-us-book-tick-by-tick[4/5]
      --rate-limit-multiplier 0.5
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books6, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-book-spot-stre-240-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-book-spot-stre-240-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bitstamp
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_0.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 240:9000
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[5/5],hetzner-us-book-tick-by-tick[5/5]
      --rate-limit-multiplier 0.5
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books6, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-book-spot-stre-000-060:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-book-spot-stre-000-060-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gemini
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_5.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:060
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-ny5-hetzner-us-book-tick-by-tick[1/3],gemini-hetzner-eu-book-tick-by-tick[1/3]
      --rate-limit-multiplier 0.1
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-book-spot-stre-060-120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-book-spot-stre-060-120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gemini
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_5.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 060:120
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-ny5-hetzner-us-book-tick-by-tick[2/3],gemini-hetzner-eu-book-tick-by-tick[2/3]
      --rate-limit-multiplier 0.1
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-book-spot-stre-120-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-book-spot-stre-120-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gemini
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_5.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 120:9000
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-ny5-hetzner-us-book-tick-by-tick[3/3],gemini-hetzner-eu-book-tick-by-tick[3/3]
      --rate-limit-multiplier 0.1
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-busy-book-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-busy-book-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime,hetzner-us-book-tick-by-tick,hetzner-eu-book-tick-by-tick
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-spot-stre-0000-0100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-spot-stre-0000-0100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 0000:0100
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-book-tick-by-tick[1/7]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-spot-stre-0100-0200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-spot-stre-0100-0200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 0100:0200
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-book-tick-by-tick[2/7]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-spot-stre-0200-0300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-spot-stre-0200-0300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 0200:0300
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-book-tick-by-tick[3/7]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-spot-stre-0300-0400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-spot-stre-0300-0400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 0300:0400
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-book-tick-by-tick[4/7]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-spot-stre-0400-0500:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-spot-stre-0400-0500-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 0400:0500
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-book-tick-by-tick[5/7]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-spot-stre-0500-0600:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-spot-stre-0500-0600-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 0500:0600
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-book-tick-by-tick[6/7]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-spot-stre-0600-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-spot-stre-0600-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments doge-usdt bsv-usdt nft-usdt ygg-usdt ldo-usdt fil-usdt auction-usdt xlm-usdt lqty-usdt ar-usdt skl-usdt sui-usdt op-usdt xch-usdt bch-usdt hft-usdt people-usdt sol-usdt avax-usdt flow-usdt ada-usdt reef-usdt zec-usdt apt-usdt arb-usdt luna-usdt polyx-usdt wld-usdt bnb-usdt near-usdt rune-usdt tia-usdt dot-usdt xtz-usdt xrp-usdt agix-usdt fet-usdt knc-usdt dia-usdt ren-usdt blur-usdt ordi-usdt uni-usdt pyth-usdt enj-usdt shib-usdt shib-usdt fxs-usdt link-usdt jst-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 0600:9000
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-book-tick-by-tick[7/7]
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  itbit-all-book-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-itbit-all-book-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper itBit
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_28.proto:3GB quotes_28:3GB
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.05
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books7, kafka_coinmetrics-kafka-zookeeper-local]

  poloniex-all-book-spot-stre-0000-0250:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-poloniex-all-book-spot-stre-0000-0250-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Poloniex
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_20.proto:3GB quotes_20:3GB
      --machine {{ inventory_hostname }}
      --market-range 0000:0250
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[1/4],hetzner-us-book-tick-by-tick[1/4],hetzner-eu-book-tick-by-tick[1/4]
      --rate-limit-multiplier 0.2
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  poloniex-all-book-spot-stre-0250-0500:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-poloniex-all-book-spot-stre-0250-0500-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Poloniex
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_20.proto:3GB quotes_20:3GB
      --machine {{ inventory_hostname }}
      --market-range 0250:0500
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[2/4],hetzner-us-book-tick-by-tick[2/4],hetzner-eu-book-tick-by-tick[2/4]
      --rate-limit-multiplier 0.2
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  poloniex-all-book-spot-stre-0500-0750:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-poloniex-all-book-spot-stre-0500-0750-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Poloniex
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_20.proto:3GB quotes_20:3GB
      --machine {{ inventory_hostname }}
      --market-range 0500:0750
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[3/4],hetzner-us-book-tick-by-tick[3/4],hetzner-eu-book-tick-by-tick[3/4]
      --rate-limit-multiplier 0.2
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  poloniex-all-book-spot-stre-0750-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-poloniex-all-book-spot-stre-0750-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Poloniex
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_20.proto:3GB quotes_20:3GB
      --machine {{ inventory_hostname }}
      --market-range 0750:9000
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[4/4],hetzner-us-book-tick-by-tick[4/4],hetzner-eu-book-tick-by-tick[4/4]
      --rate-limit-multiplier 0.2
      --spot
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]


networks:
  coinmetrics-market-books:
    external: true
    driver: overlay
  coinmetrics-market-books4:
    external: true
    driver: overlay
  coinmetrics-market-books5:
    external: true
    driver: overlay
  coinmetrics-market-books6:
    external: true
    driver: overlay
  coinmetrics-market-books7:
    external: true
    driver: overlay
  kafka_coinmetrics-kafka-zookeeper-local:
    external: true
