version: "3.7"

services:
  bitflyer-all-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitflyer-all-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper bitFlyer
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_7.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime,hetzner-us-book-tick-by-tick,hetzner-eu-book-tick-by-tick
      --rate-limit-multiplier 0.04
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0000-0080:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0000-0080-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0000:0080
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[1/8]
      bybit-prod-webshare-eu-book-realtime[1/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0080-0160:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0080-0160-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0080:0160
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[2/8]
      bybit-prod-webshare-eu-book-realtime[2/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0160-0240:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0160-0240-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0160:0240
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[3/8]
      bybit-prod-webshare-eu-book-realtime[3/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0240-0320:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0240-0320-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0240:0320
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[4/8]
      bybit-prod-webshare-eu-book-realtime[4/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0320-0400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0320-0400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0320:0400
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[5/8]
      bybit-prod-webshare-eu-book-realtime[5/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0400-0480:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0400-0480-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0400:0480
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[6/8]
      bybit-prod-webshare-eu-book-realtime[6/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0480-0560:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0480-0560-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0480:0560
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[7/8]
      bybit-prod-webshare-eu-book-realtime[7/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-all-book-futu-stre-0560-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-all-book-futu-stre-0560-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Bybit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_42.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 0560:9000
      --markets-per-producer 50
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-book-realtime[8/8]
      bybit-prod-webshare-eu-book-realtime[8/8]
      --rate-limit-multiplier 0.03
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books4, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-all-book-futu-stre-0000-0120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-all-book-futu-stre-0000-0120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gate.io
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_24.proto:3GB quotes_24:3GB
      --machine {{ inventory_hostname }}
      --market-range 0000:0120
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books7, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-all-book-futu-stre-0120-0240:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-all-book-futu-stre-0120-0240-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gate.io
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_24.proto:3GB quotes_24:3GB
      --machine {{ inventory_hostname }}
      --market-range 0120:0240
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books7, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-all-book-futu-stre-0240-0360:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-all-book-futu-stre-0240-0360-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gate.io
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_24.proto:3GB quotes_24:3GB
      --machine {{ inventory_hostname }}
      --market-range 0240:0360
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books7, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-all-book-futu-stre-0360-0480:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-all-book-futu-stre-0360-0480-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gate.io
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_24.proto:3GB quotes_24:3GB
      --machine {{ inventory_hostname }}
      --market-range 0360:0480
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books7, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-all-book-futu-stre-0480-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-all-book-futu-stre-0480-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Gate.io
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_24.proto:3GB quotes_24:3GB
      --machine {{ inventory_hostname }}
      --market-range 0480:9000
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books7, kafka_coinmetrics-kafka-zookeeper-local]


networks:
  coinmetrics-market-books4:
    external: true
    driver: overlay
  coinmetrics-market-books7:
    external: true
    driver: overlay
  kafka_coinmetrics-kafka-zookeeper-local:
    external: true
