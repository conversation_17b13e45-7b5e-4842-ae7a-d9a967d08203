version: "3.7"

services:
  binance-bchusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-bchusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments BCHUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-bonkusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-bonkusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments 1000BONKUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-btcusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-btcusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments BTCUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-dogeusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-dogeusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments DOGEUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-ethusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-ethusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments ETHUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-filusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-filusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments FILUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-flokiusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-flokiusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments 1000FLOKIUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-glmusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-glmusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments GLMUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-000-024:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-000-024-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:024
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[1/21],binance-ny5-hetzner-eu-book-realtime[1/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-024-048:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-024-048-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 024:048
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[2/21],binance-ny5-hetzner-eu-book-realtime[2/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-048-072:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-048-072-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 048:072
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[3/21],binance-ny5-hetzner-eu-book-realtime[3/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-072-096:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-072-096-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 072:096
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[4/21],binance-ny5-hetzner-eu-book-realtime[4/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-096-120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-096-120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 096:120
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[5/21],binance-ny5-hetzner-eu-book-realtime[5/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-120-144:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-120-144-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 120:144
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[6/21],binance-ny5-hetzner-eu-book-realtime[6/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-144-168:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-144-168-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 144:168
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[7/21],binance-ny5-hetzner-eu-book-realtime[7/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-168-192:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-168-192-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 168:192
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[8/21],binance-ny5-hetzner-eu-book-realtime[8/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-192-216:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-192-216-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 192:216
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[9/21],binance-ny5-hetzner-eu-book-realtime[9/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-216-240:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-216-240-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 216:240
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[10/21],binance-ny5-hetzner-eu-book-realtime[10/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-240-264:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-240-264-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 240:264
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[11/21],binance-ny5-hetzner-eu-book-realtime[11/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-264-288:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-264-288-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 264:288
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[12/21],binance-ny5-hetzner-eu-book-realtime[12/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-288-312:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-288-312-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 288:312
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[13/21],binance-ny5-hetzner-eu-book-realtime[13/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-312-336:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-312-336-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 312:336
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[14/21],binance-ny5-hetzner-eu-book-realtime[14/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-336-360:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-336-360-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 336:360
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[15/21],binance-ny5-hetzner-eu-book-realtime[15/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-360-384:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-360-384-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 360:384
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[16/21],binance-ny5-hetzner-eu-book-realtime[16/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-384-408:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-384-408-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 384:408
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[17/21],binance-ny5-hetzner-eu-book-realtime[17/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-408-432:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-408-432-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 408:432
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[18/21],binance-ny5-hetzner-eu-book-realtime[18/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-432-456:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-432-456-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 432:456
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[19/21],binance-ny5-hetzner-eu-book-realtime[19/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-456-480:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-456-480-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 456:480
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[20/21],binance-ny5-hetzner-eu-book-realtime[20/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-k-book-futu-stre-480-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-k-book-futu-stre-480-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --exclude-instruments 1000PEPEUSDT 1000SHIBUSDT 1000FLOKIUSDT WIFUSDT DOGEUSDT 1000BONKUSDT ETHUSDT BCHUSDT BTCUSDT GLMUSDT WLDUSDT MEMEUSDT FILUSDT SOLUSDT
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 480:9000
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime[21/21],binance-ny5-hetzner-eu-book-realtime[21/21]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-memeusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-memeusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments MEMEUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-pepeusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-pepeusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments 1000PEPEUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-shibusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-shibusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments 1000SHIBUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-solusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-solusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments SOLUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-wifusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-wifusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments WIFUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  binance-wldusdt-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-wldusdt-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Binance
      --futures
      --instruments WLDUSDT
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_4.proto:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-ny5-webshare-book-realtime,binance-ny5-hetzner-eu-book-realtime
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --remove-network-exchange-sequence-id
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books, kafka_coinmetrics-kafka-zookeeper-local]

  cme-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-cme-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper CME
      --api-params API_COINMETRICS_REF 7SKBXE3ZVAFZHYY2OARJCTFJEVAMD5EEDEBNQCQ= ******************** k1WDwcvZyrwiiPv5MDA56ZkoJ5EUQ2m4v0UAdNqK
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_40.proto:3GB quotes_40:3GB
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.05
      --streaming-api-params coinmetrics-f5d38728cc2a.json hel1-{{ inventory_hostname }}
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books6, kafka_coinmetrics-kafka-zookeeper-local]

  deribit-all-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-deribit-all-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Deribit
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_37.proto:3GB
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books7, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-busy-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-busy-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --futures
      --instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime,hetzner-us-book-tick-by-tick,hetzner-eu-book-tick-by-tick
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-000-012:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-000-012-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:012
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[1/15],hetzner-us-book-tick-by-tick[1/15],hetzner-eu-book-tick-by-tick[1/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-012-024:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-012-024-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 012:024
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[2/15],hetzner-us-book-tick-by-tick[2/15],hetzner-eu-book-tick-by-tick[2/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-024-036:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-024-036-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 024:036
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[3/15],hetzner-us-book-tick-by-tick[3/15],hetzner-eu-book-tick-by-tick[3/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-036-048:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-036-048-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 036:048
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[4/15],hetzner-us-book-tick-by-tick[4/15],hetzner-eu-book-tick-by-tick[4/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-048-060:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-048-060-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 048:060
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[5/15],hetzner-us-book-tick-by-tick[5/15],hetzner-eu-book-tick-by-tick[5/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-060-072:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-060-072-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 060:072
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[6/15],hetzner-us-book-tick-by-tick[6/15],hetzner-eu-book-tick-by-tick[6/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-072-084:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-072-084-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 072:084
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[7/15],hetzner-us-book-tick-by-tick[7/15],hetzner-eu-book-tick-by-tick[7/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-084-096:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-084-096-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 084:096
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[8/15],hetzner-us-book-tick-by-tick[8/15],hetzner-eu-book-tick-by-tick[8/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-096-108:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-096-108-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 096:108
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[9/15],hetzner-us-book-tick-by-tick[9/15],hetzner-eu-book-tick-by-tick[9/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-108-120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-108-120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 108:120
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[10/15],hetzner-us-book-tick-by-tick[10/15],hetzner-eu-book-tick-by-tick[10/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-120-132:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-120-132-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 120:132
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[11/15],hetzner-us-book-tick-by-tick[11/15],hetzner-eu-book-tick-by-tick[11/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-132-144:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-132-144-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 132:144
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[12/15],hetzner-us-book-tick-by-tick[12/15],hetzner-eu-book-tick-by-tick[12/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-144-156:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-144-156-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 144:156
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[13/15],hetzner-us-book-tick-by-tick[13/15],hetzner-eu-book-tick-by-tick[13/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-156-168:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-156-168-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 156:168
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[14/15],hetzner-us-book-tick-by-tick[14/15],hetzner-eu-book-tick-by-tick[14/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-k-book-futu-stre-168-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-k-book-futu-stre-168-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Huobi
      --exclude-instruments ETH-USDT_SWAP BTC-USDT_SWAP WLD-USDT_SWAP DOGE-USDT_SWAP SHIB-USDT_SWAP WIF-USDT_SWAP
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-out-quotes
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_10.proto:3GB quotes_10:3GB
      --machine {{ inventory_hostname }}
      --market-range 168:9000
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-book-realtime[15/15],hetzner-us-book-tick-by-tick[15/15],hetzner-eu-book-tick-by-tick[15/15]
      --rate-limit-multiplier 0.05
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-all-book-futu-stre-000-100:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-all-book-futu-stre-000-100-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Kraken
      --exclude-instruments PI_XBTUSD PI_ETHUSD
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_6.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 000:100
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-all-book-futu-stre-100-200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-all-book-futu-stre-100-200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Kraken
      --exclude-instruments PI_XBTUSD PI_ETHUSD
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_6.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 100:200
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-all-book-futu-stre-200-300:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-all-book-futu-stre-200-300-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Kraken
      --exclude-instruments PI_XBTUSD PI_ETHUSD
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_6.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 200:300
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-all-book-futu-stre-300-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-all-book-futu-stre-300-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Kraken
      --exclude-instruments PI_XBTUSD PI_ETHUSD
      --futures
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_6.proto:3GB
      --machine {{ inventory_hostname }}
      --market-range 300:9000
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-batch-book-futu-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-batch-book-futu-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_book_scraper Kraken
      --futures
      --instruments PI_XBTUSD PI_ETHUSD
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes books_6.proto:3GB
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --book-depth 30000
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-books5, kafka_coinmetrics-kafka-zookeeper-local]


networks:
  coinmetrics-market-books:
    external: true
    driver: overlay
  coinmetrics-market-books5:
    external: true
    driver: overlay
  coinmetrics-market-books6:
    external: true
    driver: overlay
  coinmetrics-market-books7:
    external: true
    driver: overlay
  kafka_coinmetrics-kafka-zookeeper-local:
    external: true
