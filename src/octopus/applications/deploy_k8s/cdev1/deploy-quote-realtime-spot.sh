#!/usr/bin/env bash
# Updated script to deploy sequentially helm charts

# define env variable TRACE=1 to get script execution details
[[ "${TRACE}" ]] && set -x

DEPLOYMENT_TARGET="$1"

set -eEuo pipefail
cd "$(dirname "$0")"

export DIR=.
export CHART=k8s
export HELM_CHART="oci://registry.gitlab.com/coinmetrics/ops/k8s-cm-helm/chart/k8s"
export HELM_CHART_VERSION="2.0.0-stable"
export HELM_CHART_DOWNLOAD="0"
export HELM_WAIT_FOR_RELEASE="0"

export FEED_HANDLER_INSTANCE=${FEED_HANDLER_INSTANCE:-"1"}
export HELM_RELEASE_SUFFIX="${FEED_HANDLER_INSTANCE}"

# Directory where values files are located, relative to "DIR"
export HELM_VALUE_FILES_DIR=deploy-values
# common value files with db details
export HELM_VALUE_FILES="shared-feed-handler-db-values.yaml,shared-feed-handler-db-secret-values.yaml"

# stack name - used to detect removed feed handlers between deployments and uninstall helm charts
export STACK="deploy-quote-realtime-spot"
export RELEASE_LIST="feed-handler-binance-quot-spot-stre-0000-0050,feed-handler-binance-quot-spot-stre-0050-0100,feed-handler-binance-quot-spot-stre-0100-0150,feed-handler-binance-quot-spot-stre-0150-0200,feed-handler-binance-quot-spot-stre-0200-0250,feed-handler-binance-quot-spot-stre-0250-0300,feed-handler-binance-quot-spot-stre-0300-0350,feed-handler-binance-quot-spot-stre-0350-0400,feed-handler-binance-quot-spot-stre-0400-0450,feed-handler-binance-quot-spot-stre-0450-0500,feed-handler-binance-quot-spot-stre-0500-0550,feed-handler-binance-quot-spot-stre-0550-0600,feed-handler-binance-quot-spot-stre-0600-0650,feed-handler-binance-quot-spot-stre-0650-0700,feed-handler-binance-quot-spot-stre-0700-0750,feed-handler-binance-quot-spot-stre-0750-0800,feed-handler-binance-quot-spot-stre-0800-0850,feed-handler-binance-quot-spot-stre-0850-0900,feed-handler-binance-quot-spot-stre-0900-0950,feed-handler-binance-quot-spot-stre-0950-1000,feed-handler-binance-quot-spot-stre-1000-1050,feed-handler-binance-quot-spot-stre-1050-1100,feed-handler-binance-quot-spot-stre-1100-1150,feed-handler-binance-quot-spot-stre-1150-1200,feed-handler-binance-quot-spot-stre-1200-1250,feed-handler-binance-quot-spot-stre-1250-1300,feed-handler-binance-quot-spot-stre-1300-1350,feed-handler-binance-quot-spot-stre-1350-1400,feed-handler-binance-quot-spot-stre-1400-9000,feed-handler-binanceus-quot-spot-stre,feed-handler-bitstamp-quot-spot-stre,feed-handler-bybit-quot-spot-stre-0-100,feed-handler-coinbase-quot-spot-stre-000-050,feed-handler-coinbase-quot-spot-stre-050-100,feed-handler-coinbase-quot-spot-stre-100-150,feed-handler-coinbase-quot-spot-stre-150-200,feed-handler-coinbase-quot-spot-stre-200-250,feed-handler-coinbase-quot-spot-stre-250-300,feed-handler-coinbase-quot-spot-stre-300-350,feed-handler-coinbase-quot-spot-stre-350-9000,feed-handler-coinbase-btcusd-quot-spot-stre,feed-handler-coinbase-ethusd-quot-spot-stre,feed-handler-gemini-quot-spot-stre-000-060,feed-handler-gemini-quot-spot-stre-060-120,feed-handler-gemini-quot-spot-stre-120-9000,feed-handler-hitbtc-quot-spot-stre-000-750,feed-handler-hitbtc-quot-spot-stre-750-9000,feed-handler-huobi-quot-spot-stre-000-110,feed-handler-huobi-quot-spot-stre-110-220,feed-handler-huobi-quot-spot-stre-220-330,feed-handler-huobi-quot-spot-stre-330-440,feed-handler-huobi-quot-spot-stre-440-550,feed-handler-huobi-quot-spot-stre-550-660,feed-handler-huobi-quot-spot-stre-660-9000,feed-handler-kraken-quot-spot-stre-000-400,feed-handler-kraken-quot-spot-stre-400-9000,feed-handler-okex-quot-spot-stre-000-120,feed-handler-okex-quot-spot-stre-120-240,feed-handler-okex-quot-spot-stre-240-360,feed-handler-okex-quot-spot-stre-360-9000"

# Pull the helm chart deployment package (if needed) once before running the deployment scripts in parallel
if [[ ! -d $CHART ]] ; then
  pull-helm-chart
fi

LOGDIR="/tmp/feed-handler-logs/$STACK"
mkdir -p $LOGDIR

# Individual installation of each helm chart
COUNT=0
CHUNK_SIZE=20
CHUNK_INDEX_COUNTER=0
for release in ${RELEASE_LIST//,/ }; do
  if [[ -z "$DEPLOYMENT_TARGET" ]] || [[ $(echo $release | egrep -e "$DEPLOYMENT_TARGET") ]] ; then
    if [[ ${DRYRUN:-0} -eq 0 ]] ; then
      HELM_RELEASE_NAME=$release HELM_VALUE_FILES="$HELM_VALUE_FILES,${release}-values.yaml" install-helm-chart > $LOGDIR/${release}.log 2>&1 &
      let "COUNT+=1"
    else
      echo "DRYRUN deploy: $release"
    fi
  fi
  let "CHUNK_INDEX_COUNTER+=1"
  if [[ $CHUNK_INDEX_COUNTER -eq $CHUNK_SIZE ]] ; then
    CHUNK_INDEX_COUNTER=0
    # Wait for all of the deployment scripts to complete and check for errors
    ERRORS=0
    for job in `jobs -p` ; do
      wait $job || let 'ERRORS+=1'
    done
  fi
done

# Wait for all of the deployment scripts to complete and check for errors
ERRORS=0
for job in `jobs -p` ; do
  wait $job || let 'ERRORS+=1'
done

# Dump all of the log files
for FILE in $(ls -1 $LOGDIR/*.log 2>/dev/null) ; do
  echo "-------- $(basename $FILE) --------"
  cat $FILE
done

# Only do the cleanup if there were no deployment errors and ran at least one deployment
if [[ $ERRORS -eq 0 ]] && [[ $COUNT -gt 0 ]] ; then
  # Remove unused feed handlers releases
  clean-helm-releases
fi

exit $ERRORS