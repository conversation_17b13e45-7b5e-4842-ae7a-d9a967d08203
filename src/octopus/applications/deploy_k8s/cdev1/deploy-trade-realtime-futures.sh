#!/usr/bin/env bash
# Updated script to deploy sequentially helm charts

# define env variable TRACE=1 to get script execution details
[[ "${TRACE}" ]] && set -x

DEPLOYMENT_TARGET="$1"

set -eEuo pipefail
cd "$(dirname "$0")"

export DIR=.
export CHART=k8s
export HELM_CHART="oci://registry.gitlab.com/coinmetrics/ops/k8s-cm-helm/chart/k8s"
export HELM_CHART_VERSION="2.0.0-stable"
export HELM_CHART_DOWNLOAD="0"
export HELM_WAIT_FOR_RELEASE="0"

export FEED_HANDLER_INSTANCE=${FEED_HANDLER_INSTANCE:-"1"}
export HELM_RELEASE_SUFFIX="${FEED_HANDLER_INSTANCE}"

# Directory where values files are located, relative to "DIR"
export HELM_VALUE_FILES_DIR=deploy-values
# common value files with db details
export HELM_VALUE_FILES="shared-feed-handler-db-values.yaml,shared-feed-handler-db-secret-values.yaml"

# stack name - used to detect removed feed handlers between deployments and uninstall helm charts
export STACK="deploy-trade-realtime-futures"
export RELEASE_LIST="feed-handler-binance-busy-trad-futu-http-no,feed-handler-binance-busy-trad-futu-http,feed-handler-binance-trad-futu-http-0000-0160,feed-handler-binance-trad-futu-http-0160-0320,feed-handler-binance-trad-futu-http-0320-9000,feed-handler-bullish-trad-futu-http,feed-handler-gfox-trad-futu-http,feed-handler-okex-trad-futu-http,feed-handler-binanceagg-trad-futu-stre,feed-handler-bitfinex-trad-futu-stre,feed-handler-bitflyer-trad-futu-stre,feed-handler-bitmex-trad-futu-stre,feed-handler-bullish-trad-futu-stre,feed-handler-bybit-trad-futu-stre,feed-handler-coinbaseint-trad-futu-stre,feed-handler-cme-trad-futu-stre,feed-handler-coinbaseder-trad-futu-stre,feed-handler-cryptocom-trad-futu-stre,feed-handler-deribit-trad-futu-stre,feed-handler-dydx-trad-futu-stre,feed-handler-erisx-trad-futu-stre,feed-handler-gateio-trad-futu-stre,feed-handler-hitbtc-trad-futu-stre-0-10,feed-handler-huobi-trad-futu-stre,feed-handler-kraken-trad-futu-stre,feed-handler-kucoin-trad-futu-stre,feed-handler-mexc-trad-futu-stre-000-300,feed-handler-mexc-trad-futu-stre-300-9000,feed-handler-okex-trad-futu-stre,shipper-futures-trade-exch"

# Pull the helm chart deployment package (if needed) once before running the deployment scripts in parallel
if [[ ! -d $CHART ]] ; then
  pull-helm-chart
fi

LOGDIR="/tmp/feed-handler-logs/$STACK"
mkdir -p $LOGDIR

# Individual installation of each helm chart
COUNT=0
CHUNK_SIZE=20
CHUNK_INDEX_COUNTER=0
for release in ${RELEASE_LIST//,/ }; do
  if [[ -z "$DEPLOYMENT_TARGET" ]] || [[ $(echo $release | egrep -e "$DEPLOYMENT_TARGET") ]] ; then
    if [[ ${DRYRUN:-0} -eq 0 ]] ; then
      HELM_RELEASE_NAME=$release HELM_VALUE_FILES="$HELM_VALUE_FILES,${release}-values.yaml" install-helm-chart > $LOGDIR/${release}.log 2>&1 &
      let "COUNT+=1"
    else
      echo "DRYRUN deploy: $release"
    fi
  fi
  let "CHUNK_INDEX_COUNTER+=1"
  if [[ $CHUNK_INDEX_COUNTER -eq $CHUNK_SIZE ]] ; then
    CHUNK_INDEX_COUNTER=0
    # Wait for all of the deployment scripts to complete and check for errors
    ERRORS=0
    for job in `jobs -p` ; do
      wait $job || let 'ERRORS+=1'
    done
  fi
done

# Wait for all of the deployment scripts to complete and check for errors
ERRORS=0
for job in `jobs -p` ; do
  wait $job || let 'ERRORS+=1'
done

# Dump all of the log files
for FILE in $(ls -1 $LOGDIR/*.log 2>/dev/null) ; do
  echo "-------- $(basename $FILE) --------"
  cat $FILE
done

# Only do the cleanup if there were no deployment errors and ran at least one deployment
if [[ $ERRORS -eq 0 ]] && [[ $COUNT -gt 0 ]] ; then
  # Remove unused feed handlers releases
  clean-helm-releases
fi

exit $ERRORS