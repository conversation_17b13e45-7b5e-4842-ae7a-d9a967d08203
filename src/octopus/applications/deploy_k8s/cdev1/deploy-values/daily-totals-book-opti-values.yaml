---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: daily-totals-book-opti

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.daily_totals"
  - "--postgres-write-db"
  - "$(CM_DB_OPTIONS_BOOKS_HOST):$(CM_DB_OPTIONS_BOOKS_PORT):$(CM_DB_OPTIONS_BOOKS_DATABASE):$(CM_DB_OPTIONS_BOOKS_USERNAME):$(CM_DB_OPTIONS_BOOKS_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_OPTIONS_BOOKS_HOST):$(CM_DB_OPTIONS_BOOKS_PORT):$(CM_DB_OPTIONS_BOOKS_DATABASE):$(CM_DB_OPTIONS_BOOKS_USERNAME):$(CM_DB_OPTIONS_BOOKS_PASSWORD)"
  - "--host-env"
  - "hel1"
  - "--exchanges"
  - "Binance,CME,Deribit,OKEx"
  - "--data-type"
  - "book"
  - "--option"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
