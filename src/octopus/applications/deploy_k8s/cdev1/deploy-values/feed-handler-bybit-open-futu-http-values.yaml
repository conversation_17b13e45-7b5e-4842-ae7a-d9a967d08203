#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "53m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: bybit
  feed-handler.coinmetrics.io/type: open_interest
  feed-handler.coinmetrics.io/market: futures
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: http
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.http_open_interest_scraper"
  - "Bybit"
  - "--futures"
  - "--kafka-out-proto"
  - "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "open_interests_42.proto:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--poll-interval"
  - "60"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "bybit-hel1-hetzner-eu-open-interest-realtime"
  - "--rate-limit-multiplier"
  - "0.5"
  - "--environment"
  - "cdev1"
