#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "309m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: coinbaseder
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: futures
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "CoinbaseDer"
  - "--api-params"
  - "hel1-${FEED_HANDLER_INSTANCE}:hel1-1,hel1-2"
  - "--futures"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_56.proto:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--market-source-cache-lifetime"
  - "1800"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--rate-limit-multiplier"
  - "0.05"
  - "--streaming-api-params"
  - "hel1-${FEED_HANDLER_INSTANCE}:hel1-1,hel1-2"
  - "--book-depth"
  - "30000"
  - "--disable-compute-limited-delta"
  - "--compress-books-before-send"
  - "--environment"
  - "cdev1"
