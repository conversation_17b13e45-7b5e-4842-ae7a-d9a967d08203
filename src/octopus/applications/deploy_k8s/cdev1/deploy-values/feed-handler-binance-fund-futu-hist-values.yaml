#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "47m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: binance
  feed-handler.coinmetrics.io/type: funding_rate
  feed-handler.coinmetrics.io/market: futures
  feed-handler.coinmetrics.io/collection-mode: history
  feed-handler.coinmetrics.io/connection-mode: history
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.history_funding_rate_scraper"
  - "Binance"
  - "--database"
  - "$(CM_DB_FUNDING_RATE_HOST):$(CM_DB_FUNDING_RATE_PORT):$(CM_DB_FUNDING_RATE_DATABASE):$(CM_DB_FUNDING_RATE_USERNAME):$(CM_DB_FUNDING_RATE_PASSWORD)"
  - "--cluster-machines"
  - "1"
  - "2"
  - "--exclude-proxies"
  - "http:bgtruidt:pxwspzq7gr6k:***************:5369"
  - "http:bgtruidt:pxwspzq7gr6k:*************:8288"
  - "http:bgtruidt:pxwspzq7gr6k:*************:8340"
  - "http:bgtruidt:pxwspzq7gr6k:************:8173"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7191"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7093"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7239"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7243"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7248"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7099"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7254"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7256"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7288"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7290"
  - "http:bgtruidt:pxwspzq7gr6k:**************:7305"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.225:7308"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.24:7107"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.251:7334"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.253:7336"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.254:7337"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7116"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7125"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7127"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7136"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7139"
  - "http:bgtruidt:pxwspzq7gr6k:**************:5565"
  - "http:bgtruidt:pxwspzq7gr6k:*************:5420"
  - "--exclusive-proxies"
  - "--futures"
  - "--machine"
  - "${FEED_HANDLER_INSTANCE}"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "webshare-eu-funding-rate-history,binance-hel1-hetzner-eu-funding-rate-history"
  - "--rate-limit-multiplier"
  - "0.5"
  - "--environment"
  - "cdev1"
