#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "9Gi"
   requests:
     cpu: "84m"
     memory: "9Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: binance
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: glmusdt-futures
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "Binance"
  - "--exclude-proxies"
  - "http:bgtruidt:pxwspzq7gr6k:104.239.126.101:5369"
  - "http:bgtruidt:pxwspzq7gr6k:154.13.92.197:8288"
  - "http:bgtruidt:pxwspzq7gr6k:154.13.92.249:8340"
  - "http:bgtruidt:pxwspzq7gr6k:154.13.92.82:8173"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.108:7191"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.10:7093"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.156:7239"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.160:7243"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.165:7248"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.16:7099"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.171:7254"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.173:7256"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.205:7288"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.207:7290"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.222:7305"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.225:7308"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.24:7107"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.251:7334"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.253:7336"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.254:7337"
  - "http:bgtruidt:pxwspzq7gr6k:185.102.50.33:7116"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7125"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7127"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7136"
  - "http:bgtruidt:pxwspzq7gr6k:*************:7139"
  - "http:bgtruidt:pxwspzq7gr6k:**************:5565"
  - "http:bgtruidt:pxwspzq7gr6k:*************:5420"
  - "--futures"
  - "--instruments"
  - "GLMUSDT"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_4.proto:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--markets-per-producer"
  - "1"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "binance-hel1-hetzner-eu-book-realtime,webshare-eu-book-realtime"
  - "--rate-limit-multiplier"
  - "0.01"
  - "--book-depth"
  - "30000"
  - "--remove-network-exchange-sequence-id"
  - "--environment"
  - "cdev1"
