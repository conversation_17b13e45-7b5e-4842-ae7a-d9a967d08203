---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: daily-totals-liqu-futu

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.daily_totals"
  - "--postgres-write-db"
  - "$(CM_DB_LIQUIDATIONS_HOST):$(CM_DB_LIQUIDATIONS_PORT):$(CM_DB_LIQUIDATIONS_DATABASE):$(CM_DB_LIQUIDATIONS_USERNAME):$(CM_DB_LIQUIDATIONS_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_LIQUIDATIONS_HOST):$(CM_DB_LIQUIDATIONS_PORT):$(CM_DB_LIQUIDATIONS_DATABASE):$(CM_DB_LIQUIDATIONS_USERNAME):$(CM_DB_LIQUIDATIONS_PASSWORD)"
  - "--host-env"
  - "hel1"
  - "--data-type"
  - "liquidation"
  - "--futures"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
