#!/usr/bin/env bash
# Updated script to deploy sequentially helm charts

# define env variable TRACE=1 to get script execution details
[[ "${TRACE}" ]] && set -x

DEPLOYMENT_TARGET="$1"

set -eEuo pipefail
cd "$(dirname "$0")"

export DIR=.
export CHART=k8s
export HELM_CHART="oci://registry.gitlab.com/coinmetrics/ops/k8s-cm-helm/chart/k8s"
export HELM_CHART_VERSION="2.0.0-stable"
export HELM_CHART_DOWNLOAD="0"
export HELM_WAIT_FOR_RELEASE="0"

export FEED_HANDLER_INSTANCE=${FEED_HANDLER_INSTANCE:-"1"}
export HELM_RELEASE_SUFFIX="${FEED_HANDLER_INSTANCE}"

# Directory where values files are located, relative to "DIR"
export HELM_VALUE_FILES_DIR=deploy-values
# common value files with db details
export HELM_VALUE_FILES="shared-feed-handler-db-values.yaml,shared-feed-handler-db-secret-values.yaml"

# stack name - used to detect removed feed handlers between deployments and uninstall helm charts
export STACK="deploy-book-realtime-spot"
export RELEASE_LIST="feed-handler-binanceus-book-spot-http,feed-handler-itbit-book-spot-http,feed-handler-kucoin-book-spot-http,feed-handler-binance-busy-book-spot-stre,feed-handler-binance-kafka-book-spot-stre-0-5,feed-handler-binanceus-k-book-spot-stre,feed-handler-bitbank-all-book-spot-stre,feed-handler-bitfinex-busy-book-spot-stre,feed-handler-bitfinex-book-spot-stre-0-5,feed-handler-bitflyer-all-book-spot-stre,feed-handler-bitstamp-k-book-spot-stre,feed-handler-bullish-book-spot-stre-0-50,feed-handler-bybit-k-book-spot-stre-0-5,feed-handler-coinbase-busy1-book-spot-stre,feed-handler-coinbase-busy2-book-spot-stre,feed-handler-coinbase-busy3-book-spot-stre,feed-handler-coinbase-book-spot-stre-0-5,feed-handler-cryptocom-book-spot-stre-0-5,feed-handler-erisx-book-spot-stre-0-5,feed-handler-gateio-book-spot-stre-0-5,feed-handler-gemini-book-spot-stre-000-060,feed-handler-gemini-book-spot-stre-060-120,feed-handler-gemini-book-spot-stre-120-9000,feed-handler-huobi-busy-book-spot-stre,feed-handler-huobi-k-book-spot-stre-0-5,feed-handler-itbit-all-book-spot-stre,feed-handler-kraken-k-book-spot-stre-0-5,feed-handler-lmax-book-spot-stre,feed-handler-mexc-batch-book-spot-stre-1,feed-handler-mexc-batch-book-spot-stre-2,feed-handler-mexc-batch-book-spot-stre-3,feed-handler-mexc-batch-book-spot-stre-4,feed-handler-mexc-batch-book-spot-stre-5,feed-handler-okex-all-book-spot-stre-0-5,feed-handler-poloniex-book-spot-stre-0-5,feed-handler-kucoin-book-spot-stre-0-5,shipper-spot-book"

# Pull the helm chart deployment package (if needed) once before running the deployment scripts in parallel
if [[ ! -d $CHART ]] ; then
  pull-helm-chart
fi

LOGDIR="/tmp/feed-handler-logs/$STACK"
mkdir -p $LOGDIR

# Individual installation of each helm chart
COUNT=0
CHUNK_SIZE=20
CHUNK_INDEX_COUNTER=0
for release in ${RELEASE_LIST//,/ }; do
  if [[ -z "$DEPLOYMENT_TARGET" ]] || [[ $(echo $release | egrep -e "$DEPLOYMENT_TARGET") ]] ; then
    if [[ ${DRYRUN:-0} -eq 0 ]] ; then
      HELM_RELEASE_NAME=$release HELM_VALUE_FILES="$HELM_VALUE_FILES,${release}-values.yaml" install-helm-chart > $LOGDIR/${release}.log 2>&1 &
      let "COUNT+=1"
    else
      echo "DRYRUN deploy: $release"
    fi
  fi
  let "CHUNK_INDEX_COUNTER+=1"
  if [[ $CHUNK_INDEX_COUNTER -eq $CHUNK_SIZE ]] ; then
    CHUNK_INDEX_COUNTER=0
    # Wait for all of the deployment scripts to complete and check for errors
    ERRORS=0
    for job in `jobs -p` ; do
      wait $job || let 'ERRORS+=1'
    done
  fi
done

# Wait for all of the deployment scripts to complete and check for errors
ERRORS=0
for job in `jobs -p` ; do
  wait $job || let 'ERRORS+=1'
done

# Dump all of the log files
for FILE in $(ls -1 $LOGDIR/*.log 2>/dev/null) ; do
  echo "-------- $(basename $FILE) --------"
  cat $FILE
done

# Only do the cleanup if there were no deployment errors and ran at least one deployment
if [[ $ERRORS -eq 0 ]] && [[ $COUNT -gt 0 ]] ; then
  # Remove unused feed handlers releases
  clean-helm-releases
fi

exit $ERRORS