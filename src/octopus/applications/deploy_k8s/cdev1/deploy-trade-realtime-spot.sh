#!/usr/bin/env bash
# Updated script to deploy sequentially helm charts

# define env variable TRACE=1 to get script execution details
[[ "${TRACE}" ]] && set -x

DEPLOYMENT_TARGET="$1"

set -eEuo pipefail
cd "$(dirname "$0")"

export DIR=.
export CHART=k8s
export HELM_CHART="oci://registry.gitlab.com/coinmetrics/ops/k8s-cm-helm/chart/k8s"
export HELM_CHART_VERSION="2.0.0-stable"
export HELM_CHART_DOWNLOAD="0"
export HELM_WAIT_FOR_RELEASE="0"

export FEED_HANDLER_INSTANCE=${FEED_HANDLER_INSTANCE:-"1"}
export HELM_RELEASE_SUFFIX="${FEED_HANDLER_INSTANCE}"

# Directory where values files are located, relative to "DIR"
export HELM_VALUE_FILES_DIR=deploy-values
# common value files with db details
export HELM_VALUE_FILES="shared-feed-handler-db-values.yaml,shared-feed-handler-db-secret-values.yaml"

# stack name - used to detect removed feed handlers between deployments and uninstall helm charts
export STACK="deploy-trade-realtime-spot"
export RELEASE_LIST="feed-handler-bitstamp-trad-spot-http,feed-handler-bitstamp-busy-trad-spot-http,feed-handler-bybit-trad-spot-http,feed-handler-bithumb-trad-spot-http,feed-handler-bullish-trad-spot-http,feed-handler-coinbase-trad-spot-http-0-80,feed-handler-coinbase-btcdnt-trad-spot-http,feed-handler-coinbase-ethusd-trad-spot-http,feed-handler-coinbase-ltcusd-trad-spot-http,feed-handler-cryptocom-trad-spot-http,feed-handler-gateio-trad-spot-http-0000-0900,feed-handler-gateio-trad-spot-http-0900-1800,feed-handler-gateio-trad-spot-http-1800-2700,feed-handler-gateio-trad-spot-http-2700-9000,feed-handler-gemini-trad-spot-http,feed-handler-gemini-busy-trad-spot-http,feed-handler-itbit-trad-spot-http,feed-handler-lbank-trad-spot-http,feed-handler-mexc-trad-spot-stre,feed-handler-okex-trad-spot-http,feed-handler-poloniex-trad-spot-http,feed-handler-binance-trad-spot-stre-0000-0250,feed-handler-binance-trad-spot-stre-0250-0500,feed-handler-binance-trad-spot-stre-0500-0750,feed-handler-binance-trad-spot-stre-0750-1000,feed-handler-binance-trad-spot-stre-1000-9000,feed-handler-binance-busy-trad-spot-stre,feed-handler-binanceus-trad-spot-stre,feed-handler-bitbank-trad-spot-stre,feed-handler-bitfinex-trad-spot-stre,feed-handler-bitflyer-trad-spot-stre,feed-handler-bitstamp-trad-spot-stre,feed-handler-bullish-trad-spot-stre,feed-handler-bybit-trad-spot-stre,feed-handler-coinbase-trad-spot-stre-0-150,feed-handler-coinbase-btceth-trad-spot-stre,feed-handler-cryptocom-trad-spot-stre,feed-handler-deribit-trad-spot-stre,feed-handler-erisx-trad-spot-stre,feed-handler-gateio-trad-spot-stre,feed-handler-gemini-trad-spot-stre,feed-handler-hitbtc-trad-spot-stre-0-10,feed-handler-huobi-trad-spot-stre,feed-handler-itbit-trad-spot-stre,feed-handler-kraken-trad-spot-stre,feed-handler-kucoin-trad-spot-stre,feed-handler-lbank-trad-spot-stre,feed-handler-lmax-trad-spot-stre,feed-handler-okex-trad-spot-stre,feed-handler-poloniex-trad-spot-stre,feed-handler-upbit-trad-spot-stre,shipper-spot-trade-exch"

# Pull the helm chart deployment package (if needed) once before running the deployment scripts in parallel
if [[ ! -d $CHART ]] ; then
  pull-helm-chart
fi

LOGDIR="/tmp/feed-handler-logs/$STACK"
mkdir -p $LOGDIR

# Individual installation of each helm chart
COUNT=0
CHUNK_SIZE=20
CHUNK_INDEX_COUNTER=0
for release in ${RELEASE_LIST//,/ }; do
  if [[ -z "$DEPLOYMENT_TARGET" ]] || [[ $(echo $release | egrep -e "$DEPLOYMENT_TARGET") ]] ; then
    if [[ ${DRYRUN:-0} -eq 0 ]] ; then
      HELM_RELEASE_NAME=$release HELM_VALUE_FILES="$HELM_VALUE_FILES,${release}-values.yaml" install-helm-chart > $LOGDIR/${release}.log 2>&1 &
      let "COUNT+=1"
    else
      echo "DRYRUN deploy: $release"
    fi
  fi
  let "CHUNK_INDEX_COUNTER+=1"
  if [[ $CHUNK_INDEX_COUNTER -eq $CHUNK_SIZE ]] ; then
    CHUNK_INDEX_COUNTER=0
    # Wait for all of the deployment scripts to complete and check for errors
    ERRORS=0
    for job in `jobs -p` ; do
      wait $job || let 'ERRORS+=1'
    done
  fi
done

# Wait for all of the deployment scripts to complete and check for errors
ERRORS=0
for job in `jobs -p` ; do
  wait $job || let 'ERRORS+=1'
done

# Dump all of the log files
for FILE in $(ls -1 $LOGDIR/*.log 2>/dev/null) ; do
  echo "-------- $(basename $FILE) --------"
  cat $FILE
done

# Only do the cleanup if there were no deployment errors and ran at least one deployment
if [[ $ERRORS -eq 0 ]] && [[ $COUNT -gt 0 ]] ; then
  # Remove unused feed handlers releases
  clean-helm-releases
fi

exit $ERRORS