# This file is generated by deployment_k8s_gen.py

.before-script-feed-handlers-cdev1:
  image: registry.gitlab.com/coinmetrics/ops/cicd-tools:0.2.0-stable
  variables:
    CM_FEED_HANDLER_PROMETHEUS_HOST: "[$(POD_IP)]:8080"
    CM_FEED_HANDLER_PROMETHEUS_REMOTE: "prometheus-prometheus.monitoring.svc:9090"
    ENV_NAME: "stg"
    LOCATION: "cdev1"
    KUBE_NAMESPACE: fh
  environment:
    name: staging
  when: manual
  only:
    - /.*/
  tags:
    - env-cdev1
    - rt-containerd

#
# Custom Deployments
#
1-custom-deployment-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh custom-deployment.sh cdev1

2-custom-deployment-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh custom-deployment.sh cdev1

#
# Instance #1
#

daily-totals-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/daily-totals.sh

book-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-book-realtime-futures.sh

book-realtime-option-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-book-realtime-option.sh

book-realtime-spot-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-book-realtime-spot.sh

funding-rate-data-loaders-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-funding-rate-data-loaders.sh

funding-rate-history-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-funding-rate-history-futures.sh

funding-rate-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-funding-rate-realtime-futures.sh

liquidation-data-loaders-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-liquidation-data-loaders.sh

liquidation-history-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-liquidation-history-futures.sh

liquidation-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-liquidation-realtime-futures.sh

metadata-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-metadata-realtime-futures.sh

metadata-realtime-option-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-metadata-realtime-option.sh

metadata-realtime-spot-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-metadata-realtime-spot.sh

open-interest-data-loaders-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-data-loaders.sh

open-interest-history-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-history-futures.sh

open-interest-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-realtime-futures.sh

open-interest-realtime-option-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-realtime-option.sh

quote-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-quote-realtime-futures.sh

quote-realtime-spot-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-quote-realtime-spot.sh

ticker-f-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-ticker-f-realtime-futures.sh

ticker-o-realtime-option-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-ticker-o-realtime-option.sh

trade-data-loaders-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-data-loaders.sh

trade-history-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-history-futures.sh

trade-history-option-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-history-option.sh

trade-history-spot-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-history-spot.sh

trade-realtime-futures-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-realtime-futures.sh

trade-realtime-option-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-realtime-option.sh

trade-realtime-spot-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-realtime-spot.sh

fh-alerting-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/fh-alerting.sh

fh-monitoring-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/fh-monitoring.sh

historical-patch-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/historical-patch.sh

metrics-daily-feed-handler-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/metrics-daily-feed-handler.sh

patch-job-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/patch-job.sh

proxy-check-1-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-1
  variables:
    FEED_HANDLER_INSTANCE: "1"
  script:
    - ci/deploy-scrapers.sh cdev1/proxy-check.sh

#
# Instance #2
#

book-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-book-realtime-futures.sh

book-realtime-option-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-book-realtime-option.sh

book-realtime-spot-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-book-realtime-spot.sh

funding-rate-data-loaders-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-funding-rate-data-loaders.sh

funding-rate-history-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-funding-rate-history-futures.sh

funding-rate-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-funding-rate-realtime-futures.sh

liquidation-data-loaders-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-liquidation-data-loaders.sh

liquidation-history-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-liquidation-history-futures.sh

liquidation-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-liquidation-realtime-futures.sh

metadata-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-metadata-realtime-futures.sh

metadata-realtime-option-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-metadata-realtime-option.sh

metadata-realtime-spot-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-metadata-realtime-spot.sh

open-interest-data-loaders-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-data-loaders.sh

open-interest-history-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-history-futures.sh

open-interest-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-realtime-futures.sh

open-interest-realtime-option-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-open-interest-realtime-option.sh

quote-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-quote-realtime-futures.sh

quote-realtime-spot-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-quote-realtime-spot.sh

ticker-f-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-ticker-f-realtime-futures.sh

ticker-o-realtime-option-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-ticker-o-realtime-option.sh

trade-data-loaders-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-data-loaders.sh

trade-history-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-history-futures.sh

trade-history-option-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-history-option.sh

trade-history-spot-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-history-spot.sh

trade-realtime-futures-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-realtime-futures.sh

trade-realtime-option-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-realtime-option.sh

trade-realtime-spot-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/deploy-trade-realtime-spot.sh

fh-alerting-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/fh-alerting.sh

fh-monitoring-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/fh-monitoring.sh

metrics-daily-feed-handler-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/metrics-daily-feed-handler.sh

proxy-check-2-cdev1:
  extends: .before-script-feed-handlers-cdev1
  stage: deploy-k8s:cdev1:feed-handlers-2
  variables:
    FEED_HANDLER_INSTANCE: "2"
  script:
    - ci/deploy-scrapers.sh cdev1/proxy-check.sh

