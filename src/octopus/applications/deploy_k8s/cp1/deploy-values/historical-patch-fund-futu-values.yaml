---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "8Gi"
   requests:
     cpu: "250m"
     memory: "8Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: historical-patch-fund-futu

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.historical_patch"
  - "--postgres-write-db"
  - "$(CM_DB_FUNDING_RATE_HOST):$(CM_DB_FUNDING_RATE_PORT):$(CM_DB_FUNDING_RATE_DATABASE):$(CM_DB_FUNDING_RATE_USERNAME):$(CM_DB_FUNDING_RATE_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_FUNDING_RATE_HOST):$(CM_DB_FUNDING_RATE_PORT):$(CM_DB_FUNDING_RATE_DATABASE):$(CM_DB_FUNDING_RATE_USERNAME):$(CM_DB_FUNDING_RATE_PASSWORD)"
  - "--exchanges"
  - "Binance,Bitfinex,BitMEX,Bybit,Deribit,Huobi,Kraken,OKEx"
  - "--data-type"
  - "funding_rate"
  - "--futures"
  - "--host-env"
  - "cp1"
  - "--other-env"
  - "cdev1"
  - "--begin"
  - "2024-12-01"
  - "--no-import"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
