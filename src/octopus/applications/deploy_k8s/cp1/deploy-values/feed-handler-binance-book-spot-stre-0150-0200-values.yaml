#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "9Gi"
   requests:
     cpu: "446m"
     memory: "263Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: binance
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: spot
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: 0150-0200

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "Binance"
  - "--exclude-instruments"
  - "btc-fdusd"
  - "floki-usdt"
  - "pepe-usdt"
  - "btc-usdt"
  - "shib-usdt"
  - "doge-usdt"
  - "fdusd-usdt"
  - "meme-usdt"
  - "slp-usdt"
  - "eth-usdt"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_4.proto:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--market-range"
  - "0150:0200"
  - "--markets-per-producer"
  - "30"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "binance-cp1-hetzner-eu-book-realtime[4/29]"
  - "--rate-limit-multiplier"
  - "0.01"
  - "--spot"
  - "--book-depth"
  - "30000"
  - "--remove-network-exchange-sequence-id"
  - "--environment"
  - "cp1"
