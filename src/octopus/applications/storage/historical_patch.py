import argparse
import time
from datetime import datetime
from typing import List, Optional

from src.octopus.applications.storage.util import (
    get_exchange_ids,
    get_historical_patcher,
    get_patcher_storage,
    parse_deployment_type,
    parse_exchange_ids,
    valid_begin_datetime_from_isoformat,
    valid_end_datetime_from_isoformat,
    yesterday_as_dt,
)
from src.octopus.data import MarketType
from src.octopus.inventory.types import DataType, DeploymentType
from src.octopus.storage.historical_patcher_storage import HistoricalPatcherStorage
from src.octopus.utils import get_market_type_from_args
from src.utils.application import Application, application_arguments_parser
from src.utils.diagnostics import Diagnostics
from src.utils.execution import endless_retry, execute_with_retries
from src.utils.postgres import PgConnectionParams
from src.utils.pyroutine import PyRoutine, PyRoutineSystem

INTERVAL = 600
MARKET_TYPES = [MarketType.SPOT]
DATA_TYPES = [DataType.TRADE]


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        prog="historical_patch",
        description="Import and export missing rows from --other-env",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    parser.add_argument(
        "--exchanges",
        type=parse_exchange_ids,
        required=False,
        help="Exchange names",
        default=list(get_exchange_ids()),
    )
    parser.add_argument(
        "--begin",
        type=valid_begin_datetime_from_isoformat,
        required=False,
        default=datetime(2014, 1, 1),
        help="Start date in iso format (e.g. 2022-11-18T14:00:00)",
    )
    parser.add_argument(
        "--end",
        type=valid_end_datetime_from_isoformat,
        required=False,
        help="End date in iso format (e.g. 2022-11-18T14:00:00)",
    )
    parser.add_argument(
        "--host-env",
        type=parse_deployment_type,
        choices=[DeploymentType.PROD, DeploymentType.CDEV1, DeploymentType.CP1],
        default=DeploymentType.CDEV1,
        help="Deployment environment {prod, cdev1, cp1} (default: cdev1)",
    )
    parser.add_argument(
        "--other-env",
        type=parse_deployment_type,
        choices=[DeploymentType.PROD, DeploymentType.CDEV1, DeploymentType.CP1],
        default=DeploymentType.CDEV1,
        help="Deployment environment {prod, cdev1, cp1} (default: cdev1)",
    )
    parser.add_argument(
        "--postgres-write-db",
        type=PgConnectionParams,
        required=True,
        help="host:port:db_name:user:password[+host:port:db_name:user:password]",
    )
    parser.add_argument(
        "--postgres-read-db",
        type=PgConnectionParams,
        required=True,
        help="host:port:db_name:user:password[+host:port:db_name:user:password]",
    )
    parser.add_argument("--no-export", action="store_true", help="Disable exporting of rows for --other-env")
    parser.add_argument("--no-import", action="store_true", help="Disable importing of rows from --other-env")
    parser.add_argument("--spot", action="store_true", default=False, help="Switch to spot market.")
    parser.add_argument("--futures", action="store_true", default=False, help="Switch to futures market")
    parser.add_argument("--option", action="store_true", default=False, help="Switch to option market")
    parser.add_argument("--data-type", required=True, type=lambda data_type: DataType[data_type.upper()], choices=list(DataType))
    parser.add_argument("--no-delete-exports", action="store_true", help="Disable deletion of exported rows after import")
    application_arguments_parser(parser)
    return parser.parse_args()


def historical_patch_routine(
    exchanges: List[int],
    data_type: DataType,
    market_type: MarketType,
    host_env: DeploymentType,
    other_env: DeploymentType,
    begin: datetime,
    storage: HistoricalPatcherStorage,
    diagnostics: Diagnostics,
    read_db_connection_params: PgConnectionParams,
    write_db_connection_params: PgConnectionParams,
    do_import: bool,
    do_export: bool,
    end: Optional[datetime] = None,
    delete_exports: bool = True,
) -> PyRoutine:
    env = yield None
    while env.stop_not_requested():
        with env.tick(INTERVAL):
            end_dt = end or yesterday_as_dt()
            for exchange_id in exchanges:
                diagnostics.info(f"Patch trades routine for exchange id {exchange_id}")
                patcher = get_historical_patcher(
                    data_type=data_type,
                    market_type=market_type,
                    host_env=host_env,
                    diagnostics=diagnostics,
                    storage=storage,
                    exchange_id=exchange_id,
                    read_db_connection_params=read_db_connection_params,
                    write_db_connection_params=write_db_connection_params,
                )
                if do_export:
                    if patcher.read_table_exists:
                        diagnostics.info(
                            f"Exporting exchange id {exchange_id} {data_type.name.lower()}-{market_type.name.lower()} "
                            f"rows for {other_env.name.lower()} "
                        )
                        execute_with_retries(
                            lambda: patcher.export_rows(begin=begin, end=end_dt, other_env=other_env),
                            retry_strategy=endless_retry(interval=60),
                        )
                if do_import:
                    if patcher.write_table_exists:
                        diagnostics.info(
                            f"Importing exchange id '{exchange_id}' "
                            f"{data_type.name.lower()}-{market_type.name.lower()} rows from {other_env.name.lower()}"
                        )
                        execute_with_retries(
                            lambda: patcher.import_rows(
                                begin=begin, end=end_dt, other_env=other_env, delete_export=delete_exports
                            ),
                            retry_strategy=endless_retry(interval=60),
                        )
                time.sleep(5)


def run() -> None:
    args = parse_args()
    market_type = get_market_type_from_args(args)
    with Application(
        prometheus=args.prometheus,
        log_level=args.log_level,
        log_debug_tags=args.log_debug_tags,
    ) as app:
        if args.end is not None and args.begin >= args.end:
            raise ValueError("Begin date must precede end date")
        storage = get_patcher_storage(diagnostics=app.diagnostics)
        do_import = not args.no_import
        do_export = not args.no_export
        app.diagnostics.info(f"Import: {do_import} Export: {do_export}")
        with PyRoutineSystem(app.diagnostics) as system:
            system.launch(
                historical_patch_routine(
                    host_env=args.host_env,
                    other_env=args.other_env,
                    exchanges=args.exchanges,
                    market_type=market_type,
                    data_type=args.data_type,
                    begin=args.begin,
                    end=args.end,
                    storage=storage,
                    diagnostics=app.diagnostics,
                    read_db_connection_params=args.postgres_read_db,
                    write_db_connection_params=args.postgres_write_db,
                    do_import=do_import,
                    do_export=do_export,
                    delete_exports=not args.no_delete_exports,
                ),
                name=f"historical-patch-{args.data_type.name.lower()}-{market_type.name.lower()}",
            )


if __name__ == "__main__":
    run()
