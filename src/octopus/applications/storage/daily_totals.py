import argparse
import time
from datetime import datetime
from typing import List

from src.octopus.applications.storage.util import (
    get_exchange_ids,
    get_historical_patcher,
    get_patcher_storage,
    parse_deployment_type,
    parse_exchange_ids,
    valid_begin_datetime_from_isoformat,
    yesterday_as_dt,
)
from src.octopus.data import MarketType
from src.octopus.inventory.types import DataType, DeploymentType
from src.octopus.storage.historical_patcher_storage import HistoricalPatcherStorage
from src.octopus.utils import get_market_type_from_args
from src.utils.application import Application, application_arguments_parser
from src.utils.diagnostics import Diagnostics
from src.utils.execution import endless_retry, execute_with_retries
from src.utils.postgres import PgConnectionParams
from src.utils.pyroutine import PyRoutine, PyRoutineSystem

INTERVAL = 3600
IGNORED_EXCHANGE_IDS = [
    43,  # uniswap_v1_eth
    44,  # uniswap_v2_eth
    45,  # uniswap_v3_eth
    47,  # sushiswap_v1_eth
    51,  # curve_eth
    52,  # traderjoe_v1_avaxc
]


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        prog="daily_totals",
        description="Cache row counts",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    parser.add_argument(
        "--exchanges",
        type=parse_exchange_ids,
        required=False,
        help="Exchange names",
        default=list(get_exchange_ids()),
    )
    parser.add_argument(
        "--begin",
        type=valid_begin_datetime_from_isoformat,
        required=False,
        default=datetime(2014, 1, 1),
        help="Start date in iso format (e.g. 2022-11-18T14:00:00)",
    )
    parser.add_argument(
        "--host-env",
        type=parse_deployment_type,
        choices=[DeploymentType.PROD, DeploymentType.CDEV1, DeploymentType.CP1],
        default=DeploymentType.CDEV1,
        help="Deployment environment {prod, cdev1, cp1} (default: cdev1)",
    )
    parser.add_argument(
        "--postgres-write-db",
        type=PgConnectionParams,
        required=True,
        help="host:port:db_name:user:password[+host:port:db_name:user:password]",
    )
    parser.add_argument(
        "--postgres-read-db",
        type=PgConnectionParams,
        required=True,
        help="host:port:db_name:user:password[+host:port:db_name:user:password]",
    )
    parser.add_argument("--spot", action="store_true", default=False, help="Switch to spot scraper.")
    parser.add_argument("--futures", action="store_true", default=False, help="Switch to futures scraper")
    parser.add_argument("--option", action="store_true", default=False, help="Switch to option scraper")
    parser.add_argument("--data-type", required=True, type=lambda data_type: DataType[data_type.upper()], choices=list(DataType))
    application_arguments_parser(parser)
    return parser.parse_args()


def daily_totals_routine(
    exchanges: List[int],
    data_type: DataType,
    market_type: MarketType,
    host_env: DeploymentType,
    begin: datetime,
    storage: HistoricalPatcherStorage,
    diagnostics: Diagnostics,
    read_db_connection_params: PgConnectionParams,
    write_db_connection_params: PgConnectionParams,
) -> PyRoutine:
    env = yield None
    while env.stop_not_requested():
        with env.tick(INTERVAL):
            end = yesterday_as_dt()
            for exchange_id in exchanges:
                patcher = get_historical_patcher(
                    data_type=data_type,
                    market_type=market_type,
                    host_env=host_env,
                    diagnostics=diagnostics,
                    storage=storage,
                    exchange_id=exchange_id,
                    read_db_connection_params=read_db_connection_params,
                    write_db_connection_params=write_db_connection_params,
                )
                diagnostics.info(f"Checking whether {patcher._db_table_name} exists")
                table_exists = execute_with_retries(lambda: patcher.read_table_exists, retry_strategy=endless_retry(interval=60))
                if table_exists:
                    if not patcher.write_table_exists:
                        diagnostics.error(ValueError(f"Write table {patcher._write_db_connection_params} does not exist"))
                    diagnostics.debug(
                        f"Caching {data_type.name.lower()}-{market_type.name.lower()} counts for exchange id {exchange_id}"
                    )
                    execute_with_retries(
                        lambda: patcher.get_counts(begin=begin, end=end), retry_strategy=endless_retry(interval=60)
                    )
                    time.sleep(5)


def run() -> None:
    args = parse_args()
    market_type = get_market_type_from_args(args)
    with Application(
        prometheus=args.prometheus,
        log_level=args.log_level,
        log_debug_tags=args.log_debug_tags,
    ) as app:
        app.diagnostics.info(f"Args: {args}")
        storage = get_patcher_storage(diagnostics=app.diagnostics)
        exchanges = [exchange_id for exchange_id in args.exchanges if exchange_id not in IGNORED_EXCHANGE_IDS]
        with PyRoutineSystem(app.diagnostics) as system:
            system.launch(
                daily_totals_routine(
                    host_env=args.host_env,
                    exchanges=exchanges,
                    market_type=market_type,
                    data_type=args.data_type,
                    begin=args.begin,
                    storage=storage,
                    diagnostics=app.diagnostics,
                    read_db_connection_params=args.postgres_read_db,
                    write_db_connection_params=args.postgres_write_db,
                ),
                name=f"daily-totals-{args.data_type.name.lower()}-{market_type.name.lower()}",
            )


if __name__ == "__main__":
    run()
