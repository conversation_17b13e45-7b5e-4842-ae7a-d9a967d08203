"""
Administer cached market data stored in Minio.
"""

import argparse
import sys
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from tabulate import tabulate

from src.octopus.applications.storage.util import (
    get_exchange_ids,
    get_patcher_storage,
    parse_deployment_type,
    parse_exchange_ids,
    valid_begin_datetime_from_isoformat,
)
from src.octopus.inventory.types import DataType, DeploymentType, MarketType
from src.octopus.utils import days_between
from src.resources.exchange import glib_exchange
from src.utils.diagnostics import Diagnostics

DATA_TYPES = [DataType.TRADE]
EXCHANGE_IDS_TO_IGNORE = [
    3,  # Zaif
    13,  # Bit-Z
    14,  # RightBTC
    15,  # Simex
    17,  # BCEX
    18,  # OOOBTC
    25,  # IDCM
    29,  # Ethfinex
    36,  # ICE
    43,  # uniswap_v1_eth
    44,  # uniswap_v2_eth
    45,  # uniswap_v3_eth
    47,  # sushiswap_v1_eth
    51,  # curve_eth
    52,  # traderjoe_v1_avaxc
]


def get_market_type_from_args(args: argparse.Namespace) -> MarketType:
    if args.spot:
        return MarketType.SPOT
    if args.futures:
        return MarketType.FUTURES
    if args.option:
        return MarketType.OPTION
    else:
        raise Exception("Provide ONE of --futures, --spot", "--option")


def add_args(parser: argparse.ArgumentParser) -> None:
    parser.add_argument(
        "--exchanges",
        type=parse_exchange_ids,
        required=False,
        help="Exchange names",
        default=list(get_exchange_ids()),
    )
    parser.add_argument(
        "--host-env",
        type=parse_deployment_type,
        choices=[DeploymentType.PROD, DeploymentType.CDEV1, DeploymentType.CP1],
        default=DeploymentType.CDEV1,
        help="Deployment environment {prod, cdev1, cp1} (default: cdev1)",
    )
    parser.add_argument("--data-type", required=True, type=lambda data_type: DataType[data_type.upper()], choices=list(DataType))
    parser.add_argument("--spot", action="store_true", default=False, help="Switch to spot scraper.")
    parser.add_argument("--futures", action="store_true", default=False, help="Switch to futures scraper")
    parser.add_argument("--option", action="store_true", default=False, help="Switch to option scraper")
    parser.add_argument(
        "--begin",
        type=valid_begin_datetime_from_isoformat,
        required=False,
        default=datetime(2014, 1, 1),
        help="Start date in iso format (e.g. 2022-11-18T14:00:00)",
    )
    parser.add_argument(
        "--end",
        type=valid_begin_datetime_from_isoformat,
        required=False,
        default=datetime.utcnow() - timedelta(days=2),
        help="End date in iso format (e.g. 2022-11-18T14:00:00)",
    )


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        prog="daily_totals_admin",
        description="Administer daily totals cache",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    subparsers = parser.add_subparsers(
        title="subcommands",
        description="valid subcommands",
        help="additional help",
        required=True,
        dest="action",
    )
    counts_parser = subparsers.add_parser("counts", help="Get daily counts for exchanges")
    add_args(counts_parser)
    comparison_parser = subparsers.add_parser("compare", help="Compare daily counts between envs")
    add_args(comparison_parser)
    comparison_parser.add_argument(
        "--other-env",
        type=parse_deployment_type,
        required=True,
        help="Deployment environment {prod, cdev1, cp1} (default: cdev1)",
    )
    # Add nonzero flag for compare command
    comparison_parser.add_argument(
        "--nonzero",
        action="store_true",
        help="Show only rows where there is a non-zero difference between environments",
    )
    # Add negative-diff flag for compare command
    comparison_parser.add_argument(
        "--negative-diff",
        action="store_true",
        help="Show only rows where host environment has fewer records than other environment (negative diff)",
    )
    summary_parser = subparsers.add_parser("summary", help="Get daily counts summary for exchanges")
    summary_parser.add_argument(
        "--other-env",
        type=parse_deployment_type,
        choices=[DeploymentType.PROD, DeploymentType.CDEV1, DeploymentType.CP1],
        default=DeploymentType.CDEV1,
        required=True,
        help="Deployment environment {prod, cdev1, cp1} (default: cdev1)",
    )
    # Add nonzero flag for summary command
    summary_parser.add_argument(
        "--nonzero",
        action="store_true",
        help="Show only exchanges with days requiring import or missing cache",
    )

    add_args(summary_parser)
    delete_exports_parser = subparsers.add_parser("delete-exports", help="Delete daily counts for exchanges")
    add_args(delete_exports_parser)
    args = parser.parse_args()
    if args.action == "delete-exports" and args.begin >= args.end:
        raise argparse.ArgumentError("Begin date must precede end date")

    return args


def compare_daily_totals(args: argparse.Namespace, diagnostics: Diagnostics) -> None:
    storage = get_patcher_storage(diagnostics)
    market_type = get_market_type_from_args(args)
    table = []

    for exchange_id in args.exchanges:
        if exchange_id not in EXCHANGE_IDS_TO_IGNORE:
            exchange_name = glib_exchange().exchange_name_by_id(exchange_id)
            host_env_counts = storage.get_daily_counts(
                exchange_id=exchange_id,
                data_type=args.data_type,
                market_type=market_type,
                env=args.host_env,
            )
            other_env_counts = storage.get_daily_counts(
                exchange_id=exchange_id,
                data_type=args.data_type,
                market_type=market_type,
                env=args.other_env,
            )
            days = sorted(list(set(host_env_counts.keys()).union(other_env_counts.keys())))
            comparison_days = days_between(begin=args.begin, end=args.end)

            for day in days:
                if day in comparison_days:
                    diff: Optional[int] = None
                    host_env_daily_count = host_env_counts.get(day, None)
                    other_env_daily_count = other_env_counts.get(day, None)
                    if host_env_daily_count is not None and other_env_daily_count is not None:
                        diff = host_env_daily_count - other_env_daily_count

                        # If nonzero flag is set, skip rows where diff is 0
                        if hasattr(args, "nonzero") and args.nonzero and diff == 0:
                            continue

                        # If negative-diff flag is set, skip rows where diff is non-negative
                        if hasattr(args, "negative_diff") and args.negative_diff and diff >= 0:
                            continue

                    table.append([exchange_name, day, host_env_daily_count, other_env_daily_count, diff])

    # Sort the table first by exchange name, then by day
    table.sort(key=lambda x: (x[0], x[1]))
    print(tabulate(table, headers=["EXCHANGE", "DAY", args.host_env.name.upper(), args.other_env.name.upper(), "DIFF"]))


def print_daily_totals(args: argparse.Namespace, diagnostics: Diagnostics) -> None:
    storage = get_patcher_storage(diagnostics)
    market_type = get_market_type_from_args(args)
    table = []

    for exchange_id in args.exchanges:
        if exchange_id not in EXCHANGE_IDS_TO_IGNORE:
            exchange_name = glib_exchange().exchange_name_by_id(exchange_id)
            host_env_counts = storage.get_daily_counts(
                exchange_id=exchange_id,
                data_type=args.data_type,
                market_type=market_type,
                env=args.host_env,
            )
            days = sorted(list(set(host_env_counts.keys())))
            comparison_days = days_between(begin=args.begin, end=args.end)

            for day in days:
                if day in comparison_days:
                    host_env_daily_count = host_env_counts.get(day, None)
                    table.append([exchange_name, day, host_env_daily_count])

    # Sort the table first by exchange name, then by day
    table.sort(key=lambda x: (x[0], x[1]))
    print(tabulate(table, headers=["EXCHANGE", "DAY", args.host_env.name.upper()]))


def print_summary(args: argparse.Namespace, diagnostics: Diagnostics) -> None:
    storage = get_patcher_storage(diagnostics)
    table = []
    market_type = get_market_type_from_args(args)

    for exchange_id in args.exchanges:
        if exchange_id not in EXCHANGE_IDS_TO_IGNORE:
            exchange_name = glib_exchange().exchange_name_by_id(exchange_id)
            host_env_counts = storage.get_daily_counts(
                exchange_id=exchange_id,
                data_type=args.data_type,
                market_type=market_type,
                env=args.host_env,
            )
            other_env_counts = storage.get_daily_counts(
                exchange_id=exchange_id,
                data_type=args.data_type,
                market_type=market_type,
                env=args.other_env,
            )
            days = sorted(list(set(host_env_counts.keys()).union(other_env_counts.keys())))
            comparison_days = days_between(begin=args.begin, end=args.end)
            days_requiring_import = 0
            days_with_missing_cache = 0

            for day in days:
                if day in comparison_days:
                    host_env_daily_count = host_env_counts.get(day, None)
                    other_env_daily_count = other_env_counts.get(day, None)
                    if host_env_daily_count is not None and other_env_daily_count is not None:
                        if host_env_daily_count - other_env_daily_count < 0:
                            days_requiring_import += 1
                    else:
                        days_with_missing_cache += 1

            # Skip exchanges with no issues if nonzero flag is set
            if hasattr(args, "nonzero") and args.nonzero and days_requiring_import == 0 and days_with_missing_cache == 0:
                continue

            table.append([exchange_name, days_requiring_import, days_with_missing_cache])

    # Sort the table by exchange name
    table.sort(key=lambda x: x[0])
    print(tabulate(table, headers=["Exchange", "Days Requiring Import", "Days with Missing Cache"]))


def delete_exports_between(args: argparse.Namespace, diagnostics: Diagnostics) -> None:
    storage = get_patcher_storage(diagnostics)
    market_type = get_market_type_from_args(args)
    for exchange_id in args.exchanges:
        for day in days_between(begin=args.begin, end=args.end):
            storage.delete_daily_batch_exports(
                exchange_id=exchange_id, data_type=args.data_type, market_type=market_type, env=args.host_env, day=day
            )


def run() -> None:
    args = parse_args()

    if args.action == "counts":
        print_daily_totals(args, Diagnostics())
    if args.action == "compare":
        compare_daily_totals(args, Diagnostics())
    if args.action == "summary":
        print_summary(args, Diagnostics())
    if args.action == "delete-exports":
        if not input("Delete exports for exchanges? (y/n): ").lower().strip()[:1] == "y":
            sys.exit(1)
        delete_exports_between(args, Diagnostics())


if __name__ == "__main__":
    run()
