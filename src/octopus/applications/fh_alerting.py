import traceback
from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
from datetime import datetime, UTC
from typing import List, Optional


from src.octopus.alerts.slack.bots import <PERSON>lackBot, SlackWebsocketBot, SlackHttpBot
from src.octopus.arguments import postgres_out_argument
from src.octopus.alerts.manager import <PERSON>ert<PERSON><PERSON>ger
from src.octopus.alerts.checkers.base import Checker
from src.octopus.alerts.checkers.pods import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.octopus.alerts.checkers.prometheus import <PERSON>met<PERSON><PERSON><PERSON>cker
from src.octopus.alerts.models import DataCenter
from src.octopus.alerts.sources.k8s import PodSource
from src.octopus.alerts.sources.octopus import DeploymentsSource
from src.octopus.alerts.sources.prometheus import PrometheusSource
from src.octopus.storage.postgres.alerts import AlertStorage
from src.utils.application import Application, application_arguments_parser
from src.utils.diagnostics import Diagnostics
from src.utils.execution import Tick
from src.utils.server import bind_argument
from src.utils.timeutil import get_ciso8601


def parse_data_center(value: str) -> DataCenter:
    value = value.lower()
    if value in ("prod", "production"):
        return DataCenter("production")
    elif value == "cdev1":
        return DataCenter("cdev1")
    elif value == "cp1":
        return DataCenter("cp1")
    else:
        raise ValueError(f"Unknown data center: {value} The options are: production, cdev1, cp1")


def parse_k8s_context(value: str) -> Optional[str]:
    return None if not value or value.lower() == "none" else value


def parse_cli() -> Namespace:
    arg_parser = ArgumentParser()
    arg_parser.add_argument(
        "--data-center",
        type=parse_data_center,
        default=DataCenter.CDEV1,
        required=True,
        help="Data center. Possible options: production, cdev1, cp1",
    )
    arg_parser.add_argument("--instance-number", type=int, default=1, help="Stack number in redundant architecture.")
    arg_parser.add_argument("--interval", type=int, default=360, help="The frequency of checks in seconds.")
    arg_parser.add_argument("--k8s-context", type=parse_k8s_context, default=None, help="K8s context.")
    arg_parser.add_argument(
        "--prometheus-server",
        type=bind_argument,
        default=None,
        required=True,
        help="Prometheus server host to request metrics. Format: host:port",
    )
    arg_parser.add_argument(
        "--postgres-out",
        required=True,
        nargs="+",
        default=[],
        help="host:port:db_name:user:password[+host:port:db_name:user:password] "
        "- primary and reserve alerts databases (the latter is optional)",
        type=postgres_out_argument,
    )
    arg_parser.add_argument(
        "--slack-bot-token",
        type=str,
        required=True,
        help="Slack bot token to send alerts to slack channel",
    )
    arg_parser.add_argument(
        "--slack-channel",
        type=str,
        required=True,
        help="Slack channel to send alerts",
    )
    arg_parser.add_argument(
        "--slack-app-token",
        type=str,
        required=False,
        help="Slack app token for websocket bot",
    )

    application_arguments_parser(arg_parser)
    return arg_parser.parse_args()


class Alerting:
    def __init__(
        self,
        diagnostics: Diagnostics,
        data_center: DataCenter,
        interval: int,
        instance_number: int,
        k8s_context: str,
        prometheus_server: str,
        slack_bot: SlackBot,
        storage: AlertStorage,
    ) -> None:
        self._diagnostics = diagnostics
        self._data_center = data_center
        self._interval = interval
        self._instance_number = instance_number
        self._prometheus_server = prometheus_server
        self._prometheus_host = "http://" + str(self._prometheus_server)
        self._k8s_context = k8s_context
        self._pods = PodSource(self._data_center, self._diagnostics, self._k8s_context)
        self._deployments = DeploymentsSource(self._data_center)
        self._prometheus = PrometheusSource(self._prometheus_host)
        self._slack_bot = slack_bot
        self._storage = storage

        self._alert_manager = AlertManager(
            slack_bot=self._slack_bot,
            pods_source=self._pods,
            data_center=self._data_center,
            instance_number=self._instance_number,
            diagnostics=diagnostics,
            storage=self._storage,
        )

    def run(self) -> None:
        """
        There are the following levels on which we can have issues:
        1. Hardware server - RAM, CPU shortage and servers downtime
        2. Virtual container/pod - out-of-date containers, missing, containers, stuck containers while being created
        3. Deployment configuration - wrong configuration by missing
        4. FH initialization - bugs in code on FH start
        5. Data processing logic
        6. Human error at any stage

        Levels 1-5 are going to be covered by the following pipeline in the future
        """
        self._diagnostics.info("Alerting started")

        checkers_pipeline: List[Checker] = [
            PodsChecker(
                diagnostics=self._diagnostics,
                data_center=self._data_center,
                instance=self._instance_number,
                alert_manager=self._alert_manager,
                pods_source=self._pods,
                deployments_source=self._deployments,
            ),
            PrometheusChecker(
                diagnostics=self._diagnostics,
                data_center=self._data_center,
                instance=self._instance_number,
                alert_manager=self._alert_manager,
                prometheus_source=self._prometheus,
                deployments_source=self._deployments,
            ),
        ]

        while True:
            with Tick(seconds=self._interval):
                with self._alert_manager:
                    self.run_pipeline(checkers_pipeline)

    def run_pipeline(self, checkers_pipeline: List[Checker]) -> None:
        for checker in checkers_pipeline:
            start_time = datetime.now(UTC)
            self._diagnostics.info(f"Running {checker.__class__.__name__}")
            try:
                checker.run()
            except Exception as e:
                _traceback = traceback.format_exc()
                message = f"{checker} failed with error:\n{_traceback}"
                self._diagnostics.error_ns(e, message)
                self._alert_manager.report_internal_error(message)

            self._diagnostics.info(
                f"{checker.__class__.__name__} spent {round((datetime.now(UTC) - start_time).seconds, 2)} seconds"
            )
        self._diagnostics.info(f"Checks finished {get_ciso8601(datetime.now(UTC))}")


if __name__ == "__main__":
    args = parse_cli()
    with Application(prometheus=args.prometheus, log_level=args.log_level, log_debug_tags=args.log_debug_tags) as app:
        storage = AlertStorage(args.postgres_out[0][0])  # TODO: add support for multiple databases or refactor

        app.diagnostics.info(f"Args: {args}")
        if args.instance_number == 1:
            with SlackWebsocketBot(
                app_token=args.slack_app_token,
                bot_token=args.slack_bot_token,
                storage=storage,
                channel=args.slack_channel,
                diagnostics=app.diagnostics,
            ) as slack_bot:
                Alerting(
                    diagnostics=app.diagnostics,
                    slack_bot=slack_bot,
                    data_center=args.data_center,
                    interval=args.interval,
                    instance_number=args.instance_number,
                    k8s_context=args.k8s_context,
                    prometheus_server=args.prometheus_server,
                    storage=storage,
                ).run()
        else:
            with SlackHttpBot(
                bot_token=args.slack_bot_token,
                channel=args.slack_channel,
                diagnostics=app.diagnostics,
            ) as slack_bot:
                Alerting(
                    diagnostics=app.diagnostics,
                    slack_bot=slack_bot,
                    data_center=args.data_center,
                    interval=args.interval,
                    instance_number=args.instance_number,
                    k8s_context=args.k8s_context,
                    prometheus_server=args.prometheus_server,
                    storage=storage,
                ).run()
