from typing import List, Optional

import pytest

from src.octopus.generators.utils import (
    K8S_MACHINES_SET,
    PROD_MACHINES_HOURLY_BOOKS,
    PROD_MACHINES_SET_1,
    PROD_MACHINES_SET_BOOKS,
    PROD_MACHINES_SET_QUOTES,
    get_machines,
)
from src.octopus.inventory.types import DataType, DeploymentType


@pytest.mark.parametrize("data_type", [data_type for data_type in DataType])
def test_get_machines_if_staging_should_return_all_stage_machines(data_type: DataType) -> None:
    assert get_machines(DeploymentType.CP1, data_type) == K8S_MACHINES_SET


@pytest.mark.parametrize(
    "deployment,expected_machines",
    [
        (DeploymentType.CDEV1, K8S_MACHINES_SET),
        (DeploymentType.CP1, K8S_MACHINES_SET),
    ],
)
def test_get_machines_for_k8s(deployment: DeploymentType, expected_machines: List[str]) -> None:
    for data_type in DataType:
        assert get_machines(deployment, data_type) == expected_machines


@pytest.mark.parametrize(
    "data_type,instance,expected_machines",
    [
        (DataType.BOOK, None, PROD_MACHINES_SET_BOOKS),
        (DataType.BOOK, "blah-1h-blah", PROD_MACHINES_HOURLY_BOOKS),
        (DataType.FUNDING_RATE, None, PROD_MACHINES_SET_1),
        (DataType.LIQUIDATION, None, PROD_MACHINES_SET_1),
        (DataType.METADATA, None, PROD_MACHINES_SET_1),
        (DataType.OPEN_INTEREST, None, PROD_MACHINES_SET_1),
        (DataType.QUOTE, None, PROD_MACHINES_SET_QUOTES),
        (DataType.TRADE, None, PROD_MACHINES_SET_1),
    ],
)
def test_get_machines_legacy_production(data_type: DataType, instance: Optional[str], expected_machines: List[str]) -> None:
    assert get_machines(DeploymentType.PROD, data_type, instance) == expected_machines


def test_get_machines_if_data_type_is_unknown_raise_exception() -> None:
    with pytest.raises(Exception):
        get_machines(DeploymentType.PROD, "SOMETHING_WRONG")  # type: ignore
