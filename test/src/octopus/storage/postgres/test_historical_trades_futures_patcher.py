from contextlib import contextmanager
from datetime import datetime
from decimal import Decimal
from io import String<PERSON>
from typing import Dict, Generator, Iterator
from unittest.mock import Mock

from dateutil.relativedelta import relativedelta

from src.octopus.data import Instrument, Market, MarketType, Trade, TradeData
from src.octopus.inventory.types import DataType, DeploymentType
from src.octopus.storage.historical_patcher_storage import HistoricalPatcherStorage
from src.octopus.storage.postgres.patch.db_patcher import TableColumn, TableColumns
from src.octopus.storage.postgres.patch.historical_patcher import IHistoricalPatcher
from src.octopus.storage.postgres.patch.historical_trades_futures_patcher import HistoricalTradesFuturesPatcher
from src.octopus.storage.postgres.trade import PostgresFuturesTradeStorage
from src.utils.postgres import PgConnectionParams
from test.src.octopus.storage.postgres.util import assert_patch_csv_data_contains_only_header

EXCHANGE_ID = 49
INSTRUMENT = Instrument.futures("btc-usd")
TRADES = [
    Trade(
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=TradeData(
            trade_id=1,
            price=Decimal("4.23"),
            amount=Decimal("0.5"),
            time=datetime(2022, 5, 23),
            is_buy=True,
        ),
    ),
    Trade(
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=TradeData(
            trade_id=2,
            price=Decimal("4.23"),
            amount=Decimal("0.5"),
            time=datetime(2022, 5, 23, 0, 1),
            is_buy=True,
        ),
    ),
    Trade(
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=TradeData(
            trade_id=3,
            price=Decimal("4.23"),
            amount=Decimal("0.5"),
            time=datetime(2022, 5, 24),
            is_buy=True,
        ),
    ),
]
TABLE_COLS = TableColumns(
    columns=[
        TableColumn(name="trade_id", ordinal_position=1, data_type="text"),
        TableColumn(name="trade_symbol", ordinal_position=2, data_type="text"),
        TableColumn(name="trade_amount", ordinal_position=3, data_type="text"),
        TableColumn(name="trade_price", ordinal_position=4, data_type="text"),
        TableColumn(name="trade_buy", ordinal_position=5, data_type="text"),
        TableColumn(name="trade_time", ordinal_position=6, data_type="text"),
        TableColumn(name="trade_database_time", ordinal_position=7, data_type="text"),
    ]
)
HOST_ENV = DeploymentType.CDEV1
OTHER_ENV = DeploymentType.CP1


@contextmanager
def _get_test_historical_trades_futures_patcher(
    storage: HistoricalPatcherStorage, db_connection_params: PgConnectionParams
) -> Iterator[IHistoricalPatcher]:
    yield HistoricalTradesFuturesPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        host_env=DeploymentType.CDEV1,
        storage=storage,
        read_db_connection_params=db_connection_params,
        write_db_connection_params=db_connection_params,
        get_counts_interval=0,
        batch_export_interval=0,
        batch_import_interval=0,
        table_columns=TABLE_COLS,
    )


def test_get_counts(postgres_mexc_futures_trade_storage: PostgresFuturesTradeStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 30)
    patcher_storage = Mock()
    patcher_storage.get_daily_counts = Mock(return_value={"2022-05-29": 100})
    postgres_mexc_futures_trade_storage.save_trades(TRADES)
    with _get_test_historical_trades_futures_patcher(
        patcher_storage, postgres_mexc_futures_trade_storage._connection_params
    ) as patcher:
        assert {
            "2022-05-23": 2,
            "2022-05-24": 1,
            "2022-05-25": 0,
            "2022-05-26": 0,
            "2022-05-27": 0,
            "2022-05-28": 0,
            "2022-05-29": 100,
        } == patcher.get_counts(begin=begin, end=end)


def test_export_rows(postgres_mexc_futures_trade_storage: PostgresFuturesTradeStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _side_effect(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 1,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    patcher_storage.get_daily_counts = Mock(side_effect=_side_effect)
    patcher_storage.export_exists = Mock(return_value=False)
    postgres_mexc_futures_trade_storage.save_trades(TRADES)
    with _get_test_historical_trades_futures_patcher(
        patcher_storage, postgres_mexc_futures_trade_storage._connection_params
    ) as patcher:
        patcher.export_rows(begin=begin, end=end, other_env=OTHER_ENV)
        assert 24 == patcher_storage.save_daily_batch_export.call_count
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.TRADE
        assert kwargs["market_type"] == MarketType.FUTURES
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)
        rows = kwargs["csv_data"].decode().splitlines()
        assert [
            "1",
            "btc-usd",
            "0.5",
            "4.23",
            "t",
            "2022-05-23 00:00:00+00",
        ] == rows[1].split(",")[:-1]
        assert [
            "2",
            "btc-usd",
            "0.5",
            "4.23",
            "t",
            "2022-05-23 00:01:00+00",
        ] == rows[2].split(",")[:-1]
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1


def test_import_rows(postgres_mexc_futures_trade_storage: PostgresFuturesTradeStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _get_daily_counts(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    "3,btc-usd,0.5,4.23,t,2022-05-24 00:00:00+00,2022-05-24"
                    " 00:00:00+00\n4,btc-usd,1.2,4.25,t,2022-05-24"
                    " 00:00:00+00,2022-05-24 00:00:00+00\n"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    postgres_mexc_futures_trade_storage.save_trades(TRADES)
    assert 3 == len(postgres_mexc_futures_trade_storage.get_trades_between(begin=begin, end=end))
    with _get_test_historical_trades_futures_patcher(
        patcher_storage, postgres_mexc_futures_trade_storage._connection_params
    ) as patcher:
        patcher.import_rows(begin=begin, end=end, other_env=OTHER_ENV)
        trades = postgres_mexc_futures_trade_storage.get_trades_between(begin=begin, end=end)
        assert len(trades) == 4
        assert trades[3].market.exchange_id == EXCHANGE_ID
        assert str(trades[3].market.instrument) == str(INSTRUMENT)
        assert trades[3].data.trade_id == 4
        assert trades[3].data.amount == Decimal("1.2")
        assert trades[3].data.price == Decimal("4.25")
        assert trades[3].data.is_buy is True


def test_patch(postgres_mexc_futures_trade_storage: PostgresFuturesTradeStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _export_exists(*_args: str, **_kwargs: DeploymentType) -> bool:
        if _kwargs["env"] == HOST_ENV:
            return False
        elif _kwargs["env"] == OTHER_ENV:
            return True
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_daily_counts(*_args: str, **_kwargs: DeploymentType) -> Dict[str, int]:
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    "3,btc-usd,0.5,4.23,t,2022-05-24 00:00:00+00,2022-05-24"
                    " 00:00:00+00\n4,btc-usd,1.2,4.25,t,2022-05-24"
                    " 00:00:00+00,2022-05-24 00:00:00+00\n"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    patcher_storage.export_exists = Mock(side_effect=_export_exists)

    postgres_mexc_futures_trade_storage.save_trades(TRADES)
    assert 3 == len(postgres_mexc_futures_trade_storage.get_trades_between(begin=begin, end=end))
    with _get_test_historical_trades_futures_patcher(
        patcher_storage, postgres_mexc_futures_trade_storage._connection_params
    ) as patcher:
        assert 1 == 1
        patcher.patch(begin=begin, other_env=OTHER_ENV, end=end)
        trades = postgres_mexc_futures_trade_storage.get_trades_between(begin=begin, end=end)
        assert len(trades) == 4
        assert trades[3].market.exchange_id == EXCHANGE_ID
        assert str(trades[3].market.instrument) == str(INSTRUMENT)
        assert trades[3].data.trade_id == 4
        assert trades[3].data.amount == Decimal("1.2")
        assert trades[3].data.price == Decimal("4.25")
        assert trades[3].data.is_buy is True
        assert patcher_storage.save_daily_batch_export.call_count == 24
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.TRADE
        assert kwargs["market_type"] == MarketType.FUTURES
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)
        rows = kwargs["csv_data"].decode().splitlines()
        assert [
            "1",
            "btc-usd",
            "0.5",
            "4.23",
            "t",
            "2022-05-23 00:00:00+00",
        ] == rows[1].split(",")[:-1]
        assert [
            "2",
            "btc-usd",
            "0.5",
            "4.23",
            "t",
            "2022-05-23 00:01:00+00",
        ] == rows[2].split(",")[:-1]
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1
