from contextlib import contextmanager
from datetime import datetime, timezone
from decimal import Decima<PERSON>
from io import String<PERSON>
from typing import Dict, Generator, Iterator
from unittest.mock import Mock

from dateutil.relativedelta import relativedelta

from src.octopus.data import Instrument, Market, MarketType, OptionTicker, OptionTickerData
from src.octopus.inventory.types import DataType, DeploymentType
from src.octopus.storage.historical_patcher_storage import HistoricalPatcherStorage
from src.octopus.storage.postgres.option_ticker import PostgresOptionTickerStorage
from src.octopus.storage.postgres.patch.db_patcher import TableColumn, TableColumns
from src.octopus.storage.postgres.patch.historical_patcher import IHistoricalPatcher
from src.octopus.storage.postgres.patch.historical_ticker_option_patcher import HistoricalTickerOptionPatcher
from src.utils.postgres import PgConnectionParams
from test.src.octopus.storage.postgres.util import assert_patch_csv_data_contains_only_header

EXCHANGE_ID = 49
INSTRUMENT = Instrument.option("BTCUSD")
OPTION_TICKERS = [
    OptionTicker(
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=OptionTickerData(
            time=datetime(2022, 5, 23),
            exchange_time=datetime(2022, 5, 23, tzinfo=timezone.utc),
            price_last=Decimal("4.23"),
            price_ask=Decimal("5.23"),
            price_bid=Decimal("6.23"),
            price_mark=Decimal("7.23"),
            price_index=Decimal("8.23"),
            amount_bid=Decimal("9.23"),
            amount_ask=Decimal("10.23"),
            index_name="index_name",
            estimated_settlement_price=Decimal("14.23"),
            implied_vol_trade=Decimal("15.23"),
            implied_vol_mark=Decimal("16.23"),
            implied_vol_ask=Decimal("17.23"),
            implied_vol_bid=Decimal("18.23"),
            greek_gamma=Decimal("19.23"),
            greek_delta=Decimal("20.23"),
            greek_theta=Decimal("21.23"),
            greek_vega=Decimal("22.23"),
            greek_rho=Decimal("23.23"),
        ),
    ),
    OptionTicker(
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=OptionTickerData(
            time=datetime(2022, 5, 23, 0, 1),
            exchange_time=datetime(2022, 5, 23, 0, 1, tzinfo=timezone.utc),
            price_last=Decimal("14.23"),
            price_ask=Decimal("15.23"),
            price_bid=Decimal("16.23"),
            price_mark=Decimal("17.23"),
            price_index=Decimal("18.23"),
            amount_bid=Decimal("19.23"),
            amount_ask=Decimal("20.23"),
            index_name="index_name",
            estimated_settlement_price=Decimal("24.23"),
            implied_vol_trade=Decimal("25.23"),
            implied_vol_mark=Decimal("26.23"),
            implied_vol_ask=Decimal("27.23"),
            implied_vol_bid=Decimal("28.23"),
            greek_gamma=Decimal("29.23"),
            greek_delta=Decimal("20.23"),
            greek_theta=Decimal("21.23"),
            greek_vega=Decimal("22.23"),
            greek_rho=Decimal("23.23"),
        ),
    ),
    OptionTicker(
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=OptionTickerData(
            time=datetime(2022, 5, 24),
            exchange_time=datetime(2022, 5, 25, tzinfo=timezone.utc),
            price_last=Decimal("34.23"),
            price_ask=Decimal("35.23"),
            price_bid=Decimal("36.23"),
            price_mark=Decimal("37.23"),
            price_index=Decimal("38.23"),
            amount_bid=Decimal("39.23"),
            amount_ask=Decimal("30.23"),
            index_name="index_name",
            estimated_settlement_price=Decimal("34.23"),
            implied_vol_trade=Decimal("35.23"),
            implied_vol_mark=Decimal("36.23"),
            implied_vol_ask=Decimal("37.23"),
            implied_vol_bid=Decimal("38.23"),
            greek_gamma=Decimal("39.23"),
            greek_delta=Decimal("40.23"),
            greek_theta=Decimal("41.23"),
            greek_vega=Decimal("42.23"),
            greek_rho=Decimal("43.23"),
        ),
    ),
]
TABLE_COLS = TableColumns(
    columns=[
        TableColumn(
            name="ticker_exchange_id",
            data_type="smallint",
            ordinal_position=1,
        ),
        TableColumn(
            name="ticker_symbol",
            data_type="text",
            ordinal_position=2,
        ),
        TableColumn(
            name="ticker_time",
            data_type="timestamp with time zone",
            ordinal_position=3,
        ),
        TableColumn(
            name="ticker_exchange_time",
            data_type="timestamp with time zone",
            ordinal_position=4,
        ),
        TableColumn(
            name="ticker_price_last",
            data_type="numeric",
            ordinal_position=5,
        ),
        TableColumn(
            name="ticker_price_bid",
            data_type="numeric",
            ordinal_position=6,
        ),
        TableColumn(
            name="ticker_price_ask",
            data_type="numeric",
            ordinal_position=7,
        ),
        TableColumn(
            name="ticker_price_mark",
            data_type="numeric",
            ordinal_position=8,
        ),
        TableColumn(
            name="ticker_price_index",
            data_type="numeric",
            ordinal_position=9,
        ),
        TableColumn(
            name="ticker_amount_bid",
            data_type="numeric",
            ordinal_position=10,
        ),
        TableColumn(
            name="ticker_amount_ask",
            data_type="numeric",
            ordinal_position=11,
        ),
        TableColumn(
            name="ticker_index_name",
            data_type="text",
            ordinal_position=12,
        ),
        TableColumn(
            name="ticker_implied_vol_trade",
            data_type="numeric",
            ordinal_position=13,
        ),
        TableColumn(
            name="ticker_implied_vol_bid",
            data_type="numeric",
            ordinal_position=14,
        ),
        TableColumn(
            name="ticker_implied_vol_ask",
            data_type="numeric",
            ordinal_position=15,
        ),
        TableColumn(
            name="ticker_implied_vol_mark",
            data_type="numeric",
            ordinal_position=16,
        ),
        TableColumn(
            name="ticker_greek_delta",
            data_type="numeric",
            ordinal_position=17,
        ),
        TableColumn(
            name="ticker_greek_gamma",
            data_type="numeric",
            ordinal_position=18,
        ),
        TableColumn(
            name="ticker_greek_theta",
            data_type="numeric",
            ordinal_position=19,
        ),
        TableColumn(
            name="ticker_greek_vega",
            data_type="numeric",
            ordinal_position=20,
        ),
        TableColumn(
            name="ticker_greek_rho",
            data_type="numeric",
            ordinal_position=21,
        ),
        TableColumn(
            name="ticker_database_time",
            data_type="timestamp with time zone",
            ordinal_position=22,
        ),
        TableColumn(
            name="ticker_estimated_settlement_price",
            data_type="numeric",
            ordinal_position=23,
        ),
    ]
)
HOST_ENV = DeploymentType.CDEV1
OTHER_ENV = DeploymentType.CP1


@contextmanager
def _get_test_historical_ticker_option_patcher(
    storage: HistoricalPatcherStorage, db_connection_params: PgConnectionParams
) -> Iterator[IHistoricalPatcher]:
    yield HistoricalTickerOptionPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        host_env=DeploymentType.CDEV1,
        storage=storage,
        read_db_connection_params=db_connection_params,
        write_db_connection_params=db_connection_params,
        get_counts_interval=0,
        batch_export_interval=0,
        batch_import_interval=0,
        table_columns=TABLE_COLS,
    )


def test_get_counts(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 30)
    patcher_storage = Mock()
    patcher_storage.get_daily_counts = Mock(return_value={"2022-05-29": 100})
    postgres_mexc_option_ticker_storage.save_option_ticker(OPTION_TICKERS)
    with _get_test_historical_ticker_option_patcher(
        patcher_storage, postgres_mexc_option_ticker_storage._connection_params
    ) as patcher:
        assert {
            "2022-05-23": 2,
            "2022-05-24": 1,
            "2022-05-25": 0,
            "2022-05-26": 0,
            "2022-05-27": 0,
            "2022-05-28": 0,
            "2022-05-29": 100,
        } == patcher.get_counts(begin=begin, end=end)


def test_export_rows(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _side_effect(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 1,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    patcher_storage.get_daily_counts = Mock(side_effect=_side_effect)
    patcher_storage.export_exists = Mock(return_value=False)
    postgres_mexc_option_ticker_storage.save_option_ticker(OPTION_TICKERS)
    with _get_test_historical_ticker_option_patcher(
        patcher_storage, postgres_mexc_option_ticker_storage._connection_params
    ) as patcher:
        patcher.export_rows(begin=begin, end=end, other_env=OTHER_ENV)
        assert 24 == patcher_storage.save_daily_batch_export.call_count
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.TICKER_O
        assert kwargs["market_type"] == MarketType.OPTION
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)
        rows = kwargs["csv_data"].decode().splitlines()
        assert rows[1].split(",")[:-2] == [
            "49",
            "BTCUSD",
            "2022-05-23 00:00:00+00",
            "2022-05-23 00:00:00+00",
            "4.23",
            "6.23",
            "5.23",
            "7.23",
            "8.23",
            "9.23",
            "10.23",
            "index_name",
            "15.23",
            "18.23",
            "17.23",
            "16.23",
            "20.23",
            "19.23",
            "21.23",
            "22.23",
            "23.23",
        ]
        assert rows[1].split(",")[-1] == "14.23"
        assert [
            "49",
            "BTCUSD",
            "2022-05-23 00:01:00+00",
            "2022-05-23 00:01:00+00",
            "14.23",
            "16.23",
            "15.23",
            "17.23",
            "18.23",
            "19.23",
            "20.23",
            "index_name",
            "25.23",
            "28.23",
            "27.23",
            "26.23",
            "20.23",
            "29.23",
            "21.23",
            "22.23",
            "23.23",
        ] == rows[2].split(",")[:-2]
        assert rows[2].split(",")[-1] == "24.23"
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1


def test_import_rows(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _get_daily_counts(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    f"{EXCHANGE_ID},LINKBTC,2022-5-24 14:00:00+00,2022-5-24 00:00:30.173+00,,,,0.1116,0.5088,,,"
                    f"SYN.MATIC_USDC-2AUG24,,0.0,0.0,0.7657,"
                    f"-0.97531,1.15836,-0.00024,0.00004,-0.0001,2022-5-24 00:00:38.019+00,0.5088\n"
                    f"{EXCHANGE_ID},SOLETH,2022-5-24 14:00:00+00,2022-5-24 00:00:33.113+00,,,,0.1325,0.5088,,,"
                    f"SYN.MATIC_USDC-9AUG24,,0.0,0.0,0.7083,"
                    f"-0.95051,1.50449,-0.00027,0.0001,-0.00022,2022-5-24 00:00:40.014+00,0.5088"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    postgres_mexc_option_ticker_storage.save_option_ticker(OPTION_TICKERS)
    assert 3 == len(postgres_mexc_option_ticker_storage.get_option_ticker(count=1000))
    with _get_test_historical_ticker_option_patcher(
        patcher_storage, postgres_mexc_option_ticker_storage._connection_params
    ) as patcher:
        patcher.import_rows(begin=begin, end=end, other_env=OTHER_ENV)
        option_tickers = postgres_mexc_option_ticker_storage.get_option_ticker(count=1000)
        option_tickers.sort(key=lambda x: (x.data.time, x.data.exchange_time))
        assert len(option_tickers) == 5
        assert option_tickers[3].market.exchange_id == EXCHANGE_ID
        assert str(option_tickers[3].market.instrument) == "LINKBTC-option"
        assert option_tickers[3].data.price_mark == Decimal("0.1116")
        assert option_tickers[3].data.price_index == Decimal("0.5088")
        assert option_tickers[3].data.estimated_settlement_price == Decimal("0.5088")
        assert option_tickers[3].data.price_ask is None
        assert option_tickers[3].data.price_bid is None
        assert option_tickers[3].data.index_name == "SYN.MATIC_USDC-2AUG24"
        assert option_tickers[3].data.amount_ask is None
        assert option_tickers[3].data.amount_bid is None
        assert option_tickers[3].data.implied_vol_ask == Decimal("0.0")
        assert option_tickers[3].data.implied_vol_bid == Decimal("0.0")
        assert option_tickers[3].data.implied_vol_mark == Decimal("0.7657")
        assert option_tickers[3].data.greek_delta == Decimal("-0.97531")
        assert option_tickers[3].data.greek_gamma == Decimal("1.15836")
        assert option_tickers[3].data.greek_rho == Decimal("-0.0001")
        assert option_tickers[3].data.greek_theta == Decimal("-0.00024")


def test_patch(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _export_exists(*_args: str, **_kwargs: DeploymentType) -> bool:
        if _kwargs["env"] == HOST_ENV:
            return False
        elif _kwargs["env"] == OTHER_ENV:
            return True
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_daily_counts(*_args: str, **_kwargs: DeploymentType) -> Dict[str, int]:
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    f"{EXCHANGE_ID},LINKBTC,2022-5-24 14:00:00+00,2022-5-24 00:00:30.173+00,,,,0.1116,0.5088,,,"
                    f"SYN.MATIC_USDC-2AUG24,,0.0,0.0,0.7657,-0.97531,1.15836,-0.00024,0.00004,-0.0001,"
                    f"2022-5-24 00:00:38.019+00,0.5088\n"
                    f"{EXCHANGE_ID},SOLETH,2022-5-24 14:00:00+00,2022-5-24 00:00:33.113+00,,,,0.1325,0.5088,,,"
                    f"SYN.MATIC_USDC-9AUG24,,0.0,0.0,0.7083,-0.95051,1.50449,-0.00027,0.0001,-0.00022,"
                    f"2022-5-24 00:00:40.014+00,0.5088"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    patcher_storage.export_exists = Mock(side_effect=_export_exists)

    postgres_mexc_option_ticker_storage.save_option_ticker(OPTION_TICKERS)
    assert 3 == len(postgres_mexc_option_ticker_storage.get_option_ticker(1000))
    with _get_test_historical_ticker_option_patcher(
        patcher_storage, postgres_mexc_option_ticker_storage._connection_params
    ) as patcher:
        patcher.patch(begin=begin, other_env=OTHER_ENV, end=end)
        option_tickers = postgres_mexc_option_ticker_storage.get_option_ticker(1000)
        assert len(option_tickers) == 5
        assert option_tickers[3].market.exchange_id == EXCHANGE_ID
        assert str(option_tickers[3].market.instrument) == "LINKBTC-option"
        assert option_tickers[3].data.price_mark == Decimal("0.1116")
        assert option_tickers[3].data.price_index == Decimal("0.5088")
        assert option_tickers[3].data.estimated_settlement_price == Decimal("0.5088")
        assert option_tickers[3].data.price_ask is None
        assert option_tickers[3].data.price_bid is None
        assert option_tickers[3].data.index_name == "SYN.MATIC_USDC-2AUG24"
        assert option_tickers[3].data.amount_ask is None
        assert option_tickers[3].data.amount_bid is None
        assert option_tickers[3].data.implied_vol_ask == Decimal("0.0")
        assert option_tickers[3].data.implied_vol_bid == Decimal("0.0")
        assert option_tickers[3].data.implied_vol_mark == Decimal("0.7657")
        assert option_tickers[3].data.greek_delta == Decimal("-0.97531")
        assert option_tickers[3].data.greek_gamma == Decimal("1.15836")
        assert option_tickers[3].data.greek_rho == Decimal("-0.0001")
        assert option_tickers[3].data.greek_theta == Decimal("-0.00024")
        assert patcher_storage.save_daily_batch_export.call_count == 24
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.TICKER_O
        assert kwargs["market_type"] == MarketType.OPTION
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)

        rows = kwargs["csv_data"].decode().splitlines()
        assert rows[1].split(",")[:-2] == [
            "49",
            "BTCUSD",
            "2022-05-23 00:00:00+00",
            "2022-05-23 00:00:00+00",
            "4.23",
            "6.23",
            "5.23",
            "7.23",
            "8.23",
            "9.23",
            "10.23",
            "index_name",
            "15.23",
            "18.23",
            "17.23",
            "16.23",
            "20.23",
            "19.23",
            "21.23",
            "22.23",
            "23.23",
        ]
        assert rows[1].split(",")[-1] == "14.23"
        assert [
            "49",
            "BTCUSD",
            "2022-05-23 00:01:00+00",
            "2022-05-23 00:01:00+00",
            "14.23",
            "16.23",
            "15.23",
            "17.23",
            "18.23",
            "19.23",
            "20.23",
            "index_name",
            "25.23",
            "28.23",
            "27.23",
            "26.23",
            "20.23",
            "29.23",
            "21.23",
            "22.23",
            "23.23",
        ] == rows[2].split(",")[:-2]
        assert rows[2].split(",")[-1] == "24.23"
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1
