from contextlib import contextmanager
from datetime import datetime, timezone
from io import <PERSON><PERSON>
from typing import Dict, Generator, Iterator
from unittest.mock import Mock

from dateutil.relativedelta import relativedelta

from src.octopus.data import Book, BookData, BookType, Instrument, Market, MarketType
from src.octopus.inventory.types import DataType, DeploymentType
from src.octopus.storage.historical_patcher_storage import HistoricalPatcherStorage
from src.octopus.storage.postgres.book_old import PostgresOldSpotBookStorage
from src.octopus.storage.postgres.patch.db_patcher import TableColumn, TableColumns
from src.octopus.storage.postgres.patch.historical_book_spot_patcher import HistoricalBookSpotPatcher
from src.octopus.storage.postgres.patch.historical_patcher import IHistoricalPatcher
from src.utils.postgres import PgConnectionParams
from test.src.octopus.storage.postgres.util import assert_patch_csv_data_contains_only_header

EXCHANGE_ID = 49
INSTRUMENT = Instrument.spot(symbol="BTCUSDT", base="btc", quote="usdt", alt_symbol="BTC-USDT")
MARKET = Market(EXCHANGE_ID, INSTRUMENT)

REFERENCE_TIME = datetime(2022, 5, 23, tzinfo=timezone.utc)
DEPTH = 100
SESSION_ID = 0  # Not saved/read from DB
SEQUENCE_ID = 0  # Not saved/read from DB
COLLECT_TIME = datetime(2022, 5, 23, tzinfo=timezone.utc)
SPOT_BOOKS = [
    Book(
        market=MARKET,
        depth_limit=DEPTH,
        collect_time=COLLECT_TIME,
        deduplication_time=REFERENCE_TIME,
        scraper_session_id=SESSION_ID,
        scraper_sequence_id=SEQUENCE_ID,
        data=BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=1,
            exchange_time=COLLECT_TIME,
            bids=[],
            asks=[],
        ),
    ),
    Book(
        market=MARKET,
        depth_limit=DEPTH,
        collect_time=COLLECT_TIME + relativedelta(minutes=10),
        deduplication_time=REFERENCE_TIME + relativedelta(minutes=10),
        scraper_session_id=SESSION_ID,
        scraper_sequence_id=SEQUENCE_ID,
        data=BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=1,
            exchange_time=COLLECT_TIME + relativedelta(minutes=10),
            bids=[],
            asks=[],
        ),
    ),
    Book(
        market=MARKET,
        depth_limit=DEPTH,
        collect_time=COLLECT_TIME + relativedelta(days=1),
        deduplication_time=REFERENCE_TIME + relativedelta(days=1),
        scraper_session_id=SESSION_ID,
        scraper_sequence_id=SEQUENCE_ID,
        data=BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=1,
            exchange_time=COLLECT_TIME + relativedelta(days=1),
            bids=[],
            asks=[],
        ),
    ),
]
TABLE_COLS = TableColumns(
    columns=[
        TableColumn(name="book_exchange_id", ordinal_position=1, data_type="smallint"),
        TableColumn(name="book_base_id", ordinal_position=2, data_type="smallint"),
        TableColumn(name="book_quote_id", ordinal_position=3, data_type="smallint"),
        TableColumn(name="book_depth_limit", ordinal_position=4, data_type="integer"),
        TableColumn(name="book_time", ordinal_position=5, data_type="timestamp with time zone"),
        TableColumn(name="book_database_time", ordinal_position=6, data_type="timestamp with time zone"),
        TableColumn(name="book_exchange_sequence_id", ordinal_position=7, data_type="numeric"),
        TableColumn(name="book_exchange_time", ordinal_position=8, data_type="timestamp with time zone"),
        TableColumn(name="book_bids", ordinal_position=9, data_type="ARRAY"),
        TableColumn(name="book_asks", ordinal_position=10, data_type="ARRAY"),
    ]
)
HOST_ENV = DeploymentType.CDEV1
OTHER_ENV = DeploymentType.CP1


@contextmanager
def _get_test_historical_book_spot_patcher(
    storage: HistoricalPatcherStorage, db_connection_params: PgConnectionParams
) -> Iterator[IHistoricalPatcher]:
    yield HistoricalBookSpotPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        host_env=DeploymentType.CDEV1,
        storage=storage,
        read_db_connection_params=db_connection_params,
        write_db_connection_params=db_connection_params,
        get_counts_interval=0,
        batch_export_interval=0,
        batch_import_interval=0,
        table_columns=TABLE_COLS,
    )


def test_get_counts(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 30)
    patcher_storage = Mock()
    patcher_storage.get_daily_counts = Mock(return_value={"2022-05-29": 100})
    postgres_old_spot_book_storage.save_books(SPOT_BOOKS)
    with _get_test_historical_book_spot_patcher(patcher_storage, postgres_old_spot_book_storage._connection_params) as patcher:
        assert {
            "2022-05-23": 2,
            "2022-05-24": 1,
            "2022-05-25": 0,
            "2022-05-26": 0,
            "2022-05-27": 0,
            "2022-05-28": 0,
            "2022-05-29": 100,
        } == patcher.get_counts(begin=begin, end=end)


def test_export_rows(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _side_effect(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 1,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    patcher_storage.get_daily_counts = Mock(side_effect=_side_effect)
    patcher_storage.export_exists = Mock(return_value=False)
    postgres_old_spot_book_storage.save_books(SPOT_BOOKS)
    with _get_test_historical_book_spot_patcher(patcher_storage, postgres_old_spot_book_storage._connection_params) as patcher:
        patcher.export_rows(begin=begin, end=end, other_env=OTHER_ENV)
        assert 24 == patcher_storage.save_daily_batch_export.call_count
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.BOOK
        assert kwargs["market_type"] == MarketType.SPOT
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)
        rows = kwargs["csv_data"].decode().splitlines()
        csv_book = rows[1].split(",")
        assert csv_book[0] == "49"
        assert csv_book[1] == "0"
        assert csv_book[2] == "100"
        assert csv_book[3] == "100"
        assert csv_book[4] == "2022-05-23 00:00:00+00"
        assert csv_book[6] == "1"
        assert csv_book[7] == "2022-05-23 00:00:00+00"
        assert csv_book[8] == "{}"
        assert csv_book[9] == "{}"
        csv_book = rows[2].split(",")
        assert csv_book[0] == "49"
        assert csv_book[1] == "0"
        assert csv_book[2] == "100"
        assert csv_book[3] == "100"
        assert csv_book[4] == "2022-05-23 00:10:00+00"
        assert csv_book[6] == "1"
        assert csv_book[7] == "2022-05-23 00:10:00+00"
        assert csv_book[8] == "{}"
        assert csv_book[9] == "{}"
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1


def test_import_rows(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _get_daily_counts(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    "49,0,100,100,2022-05-23 00:20:00+00,2024-10-06 10:23:55.831025+00,1,2022-05-23 00:20:00+00,{},{}\n"
                    "49,0,100,100,2022-05-23 00:30:00+00,2024-10-06 10:23:55.831025+00,1,2022-05-23 00:30:00+00,{},{}\n"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    postgres_old_spot_book_storage.save_books(SPOT_BOOKS)
    assert 3 == len(postgres_old_spot_book_storage.get_books(count=1000))
    with _get_test_historical_book_spot_patcher(patcher_storage, postgres_old_spot_book_storage._connection_params) as patcher:
        patcher.import_rows(begin=begin, end=end, other_env=OTHER_ENV)
        spot_books = postgres_old_spot_book_storage.get_books(count=1000)
        assert len(spot_books) == 5
        assert spot_books[3].market.exchange_id == EXCHANGE_ID
        assert spot_books[3].market.instrument.base == INSTRUMENT.base
        assert spot_books[3].market.instrument.quote == INSTRUMENT.quote
        assert spot_books[3].data.exchange_sequence_id == 1
        assert spot_books[3].data.exchange_time == datetime(2022, 5, 23, 0, 20, tzinfo=timezone.utc)
        assert spot_books[3].deduplication_time == datetime(2022, 5, 23, 0, 20, tzinfo=timezone.utc)
        assert spot_books[3].data.bids == []
        assert spot_books[3].data.asks == []


def test_patch(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _export_exists(*_args: str, **_kwargs: DeploymentType) -> bool:
        if _kwargs["env"] == HOST_ENV:
            return False
        elif _kwargs["env"] == OTHER_ENV:
            return True
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_daily_counts(*_args: str, **_kwargs: DeploymentType) -> Dict[str, int]:
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    "49,1,100,100,2022-05-24 00:20:00+00,2024-10-06 10:23:55.831025+00,1,2022-05-24 00:20:00+00,{},{}\n"
                    "49,1,100,100,2022-05-24 00:30:00+00,2024-10-06 10:23:55.831025+00,1,2022-05-24 00:30:00+00,{},{}\n"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    patcher_storage.export_exists = Mock(side_effect=_export_exists)

    postgres_old_spot_book_storage.save_books(SPOT_BOOKS)
    assert 3 == len(postgres_old_spot_book_storage.get_books_between(begin, end))
    with _get_test_historical_book_spot_patcher(patcher_storage, postgres_old_spot_book_storage._connection_params) as patcher:
        patcher.patch(begin=begin, other_env=OTHER_ENV, end=end)
        spot_books = postgres_old_spot_book_storage.get_books_between(begin, end)
        assert len(spot_books) == 5
        assert spot_books[3].market.exchange_id == EXCHANGE_ID
        assert spot_books[3].market.instrument.base == 1
        assert spot_books[3].market.instrument.quote == 100
        assert spot_books[3].data.exchange_sequence_id == 1
        assert spot_books[3].data.exchange_time == datetime(2022, 5, 24, 0, 20, tzinfo=timezone.utc)
        assert spot_books[3].deduplication_time == datetime(2022, 5, 24, 0, 20, tzinfo=timezone.utc)
        assert spot_books[3].data.bids == []
        assert spot_books[3].data.asks == []
        assert patcher_storage.save_daily_batch_export.call_count == 24
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.BOOK
        assert kwargs["market_type"] == MarketType.SPOT
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)
        rows = kwargs["csv_data"].decode().splitlines()
        csv_book = rows[1].split(",")
        assert csv_book[0] == "49"
        assert csv_book[1] == "0"
        assert csv_book[2] == "100"
        assert csv_book[3] == "100"
        assert csv_book[4] == "2022-05-23 00:00:00+00"
        assert csv_book[6] == "1"
        assert csv_book[7] == "2022-05-23 00:00:00+00"
        assert csv_book[8] == "{}"
        assert csv_book[9] == "{}"
        csv_book = rows[2].split(",")
        assert csv_book[0] == "49"
        assert csv_book[1] == "0"
        assert csv_book[2] == "100"
        assert csv_book[3] == "100"
        assert csv_book[4] == "2022-05-23 00:10:00+00"
        assert csv_book[6] == "1"
        assert csv_book[7] == "2022-05-23 00:10:00+00"
        assert csv_book[8] == "{}"
        assert csv_book[9] == "{}"
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1
