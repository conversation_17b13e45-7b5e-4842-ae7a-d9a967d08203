from contextlib import contextmanager
from datetime import datetime, timezone
from decimal import Decimal
from io import String<PERSON>
from typing import Dict, Generator, Iterator
from unittest.mock import Mock

from dateutil.relativedelta import relativedelta

from src.octopus.data import FuturesTicker, FuturesTickerData, Instrument, Market, MarketType
from src.octopus.inventory.types import DataType, DeploymentType
from src.octopus.storage.historical_patcher_storage import HistoricalPatcherStorage
from src.octopus.storage.postgres.futures_ticker import PostgresFuturesTickerStorage
from src.octopus.storage.postgres.patch.db_patcher import TableColumn, TableColumns
from src.octopus.storage.postgres.patch.historical_patcher import IHistoricalPatcher
from src.octopus.storage.postgres.patch.historical_ticker_futures_patcher import HistoricalTickerFuturesPatcher
from src.utils.postgres import PgConnectionParams
from test.src.octopus.storage.postgres.util import assert_patch_csv_data_contains_only_header

EXCHANGE_ID = 49
INSTRUMENT = Instrument.futures("BTCUSD")
FUTURES_TICKERS = [
    FuturesTicker(
        deduplication_time=datetime(2022, 5, 23),
        time=datetime(2022, 5, 23),
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=FuturesTickerData(
            exchange_time=datetime(2022, 5, 23, tzinfo=timezone.utc),
            price_mark=Decimal("4.23"),
            price_index=Decimal("4.23"),
            estimated_settlement_price=Decimal("4.23"),
            estimated_funding_rate=Decimal("0.0"),
            estimated_funding_rate_time=None,
        ),
    ),
    FuturesTicker(
        deduplication_time=datetime(2022, 5, 23, 0, 1),
        time=datetime(2022, 5, 23, 0, 1),
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=FuturesTickerData(
            exchange_time=datetime(2022, 5, 23, 0, 1, tzinfo=timezone.utc),
            price_mark=Decimal("4.23"),
            price_index=Decimal("4.23"),
            estimated_settlement_price=Decimal("4.23"),
            estimated_funding_rate=Decimal("0.0"),
            estimated_funding_rate_time=None,
        ),
    ),
    FuturesTicker(
        deduplication_time=datetime(2022, 5, 24),
        time=datetime(2022, 5, 24),
        market=Market(exchange_id=EXCHANGE_ID, instrument=INSTRUMENT),
        data=FuturesTickerData(
            exchange_time=datetime(2022, 5, 24, tzinfo=timezone.utc),
            price_mark=Decimal("4.23"),
            price_index=Decimal("4.23"),
            estimated_settlement_price=Decimal("4.23"),
            estimated_funding_rate=Decimal("0.0"),
            estimated_funding_rate_time=None,
        ),
    ),
]
TABLE_COLS = TableColumns(
    columns=[
        TableColumn(name="ticker_symbol", data_type="text", ordinal_position=1),
        TableColumn(name="ticker_deduplication_time", data_type="timestamp with time zone", ordinal_position=2),
        TableColumn(name="ticker_time", data_type="timestamp with time zone", ordinal_position=3),
        TableColumn(name="ticker_price_mark", data_type="numeric", ordinal_position=4),
        TableColumn(name="ticker_price_index", data_type="numeric", ordinal_position=5),
        TableColumn(name="ticker_estimated_settlement_price", data_type="numeric", ordinal_position=6),
        TableColumn(name="ticker_estimated_funding_rate", data_type="numeric", ordinal_position=7),
        TableColumn(name="ticker_estimated_funding_rate_time", data_type="timestamp with time zone", ordinal_position=8),
        TableColumn(name="ticker_database_time", data_type="timestamp with time zone", ordinal_position=9),
    ]
)
HOST_ENV = DeploymentType.CDEV1
OTHER_ENV = DeploymentType.CP1


@contextmanager
def _get_test_historical_ticker_futures_patcher(
    storage: HistoricalPatcherStorage, db_connection_params: PgConnectionParams
) -> Iterator[IHistoricalPatcher]:
    yield HistoricalTickerFuturesPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        host_env=DeploymentType.CDEV1,
        storage=storage,
        read_db_connection_params=db_connection_params,
        write_db_connection_params=db_connection_params,
        get_counts_interval=0,
        batch_export_interval=0,
        batch_import_interval=0,
        table_columns=TABLE_COLS,
    )


def test_get_counts(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 30)
    patcher_storage = Mock()
    patcher_storage.get_daily_counts = Mock(return_value={"2022-05-29": 100})
    postgres_mexc_futures_ticker_storage.save_futures_ticker(FUTURES_TICKERS)
    with _get_test_historical_ticker_futures_patcher(
        patcher_storage, postgres_mexc_futures_ticker_storage._connection_params
    ) as patcher:
        assert {
            "2022-05-23": 2,
            "2022-05-24": 1,
            "2022-05-25": 0,
            "2022-05-26": 0,
            "2022-05-27": 0,
            "2022-05-28": 0,
            "2022-05-29": 100,
        } == patcher.get_counts(begin=begin, end=end)


def test_export_rows(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _side_effect(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 1,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    patcher_storage.get_daily_counts = Mock(side_effect=_side_effect)
    patcher_storage.export_exists = Mock(return_value=False)
    postgres_mexc_futures_ticker_storage.save_futures_ticker(FUTURES_TICKERS)
    with _get_test_historical_ticker_futures_patcher(
        patcher_storage, postgres_mexc_futures_ticker_storage._connection_params
    ) as patcher:
        patcher.export_rows(begin=begin, end=end, other_env=OTHER_ENV)
        assert 24 == patcher_storage.save_daily_batch_export.call_count
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.TICKER_F
        assert kwargs["market_type"] == MarketType.FUTURES
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)
        rows = kwargs["csv_data"].decode().splitlines()
        assert rows[1].split(",")[:-1] == [
            "BTCUSD",
            "2022-05-23 00:00:00+00",
            "2022-05-23 00:00:00+00",
            "4.23",
            "4.23",
            "4.23",
            "0.0",
            "",
        ]
        assert ["BTCUSD", "2022-05-23 00:01:00+00", "2022-05-23 00:01:00+00", "4.23", "4.23", "4.23", "0.0", ""] == rows[
            2
        ].split(",")[:-1]
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1


def test_import_rows(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _get_daily_counts(*_args, **_kwargs) -> Dict[str, int]:  # type: ignore[no-untyped-def]
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    "BTCUSD,2022-5-24 01:01:00+00,2022-5-24 01:01:00+00,68176.99,66708.56,0.00,,,"
                    "2022-5-24 01:01:04.066+00\n"
                    "BTCUSD,2022-5-24 01:02:00+00,2022-5-24 01:02:00+00,68176.99,66708.56,0.00,,,"
                    "2022-5-24 01:02:04.066+00\n"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    postgres_mexc_futures_ticker_storage.save_futures_ticker(FUTURES_TICKERS)
    assert 3 == len(postgres_mexc_futures_ticker_storage.get_futures_ticker(count=1000))
    with _get_test_historical_ticker_futures_patcher(
        patcher_storage, postgres_mexc_futures_ticker_storage._connection_params
    ) as patcher:
        patcher.import_rows(begin=begin, end=end, other_env=OTHER_ENV)
        futures_tickers = postgres_mexc_futures_ticker_storage.get_futures_ticker(count=1000)
        assert len(futures_tickers) == 5
        assert futures_tickers[3].market.exchange_id == EXCHANGE_ID
        assert str(futures_tickers[3].market.instrument) == str(INSTRUMENT)
        assert futures_tickers[3].data.price_mark == Decimal("68176.99")
        assert futures_tickers[3].data.price_index == Decimal("66708.56")
        assert futures_tickers[3].data.estimated_settlement_price == Decimal("0.00")
        assert futures_tickers[3].data.estimated_funding_rate is None
        assert futures_tickers[3].data.estimated_funding_rate_time is None
        assert futures_tickers[3].data.exchange_time is None  # exchange_time is never saved to DB
        assert futures_tickers[3].deduplication_time == datetime(2022, 5, 24, 1, 1, tzinfo=timezone.utc)
        assert futures_tickers[3].time == datetime(2022, 5, 24, 1, 1, tzinfo=timezone.utc)


def test_patch(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    begin = datetime(2022, 5, 23)
    end = datetime(2022, 5, 25)
    patcher_storage = Mock()

    def _export_exists(*_args: str, **_kwargs: DeploymentType) -> bool:
        if _kwargs["env"] == HOST_ENV:
            return False
        elif _kwargs["env"] == OTHER_ENV:
            return True
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_daily_counts(*_args: str, **_kwargs: DeploymentType) -> Dict[str, int]:
        if _kwargs["env"] == HOST_ENV:
            return {
                "2022-05-23": 2,
                "2022-05-24": 1,
            }
        elif _kwargs["env"] == OTHER_ENV:
            return {
                "2022-05-23": 1,
                "2022-05-24": 2,
            }
        else:
            raise ValueError(f"Unhandled mock call with kwargs {_kwargs}")

    def _get_export_chunks(*_args, **_kwargs) -> Generator[StringIO, None, None]:  # type: ignore[no-untyped-def]
        csv_data = ""
        if _kwargs["env"] == OTHER_ENV:
            if _kwargs["begin"] == datetime(2022, 5, 24, 0) and _kwargs["end"] == datetime(2022, 5, 24, 1):
                csv_data = (
                    ",".join(TABLE_COLS.ordered_column_names) + "\n"
                    "BTCUSD,2022-5-24 01:01:00+00,2022-5-24 01:01:00+00,68176.99,66708.56,0.00,,,"
                    "2022-5-24 01:01:04.066+00\n"
                    "BTCUSD,2022-5-24 01:02:00+00,2022-5-24 01:02:00+00,68176.99,66708.56,0.00,,,"
                    "2022-5-24 01:02:04.066+00\n"
                )
            for line in csv_data.splitlines():
                yield StringIO(line)
        else:
            raise ValueError("Unsupported call to mock function _get_export")

    patcher_storage.get_daily_counts = Mock(side_effect=_get_daily_counts)
    patcher_storage.get_export_chunks = Mock(side_effect=_get_export_chunks)
    patcher_storage.export_exists = Mock(side_effect=_export_exists)

    postgres_mexc_futures_ticker_storage.save_futures_ticker(FUTURES_TICKERS)
    assert 3 == len(postgres_mexc_futures_ticker_storage.get_futures_ticker(1000))
    with _get_test_historical_ticker_futures_patcher(
        patcher_storage, postgres_mexc_futures_ticker_storage._connection_params
    ) as patcher:
        patcher.patch(begin=begin, other_env=OTHER_ENV, end=end)
        futures_tickers = postgres_mexc_futures_ticker_storage.get_futures_ticker(1000)
        assert len(futures_tickers) == 5
        assert futures_tickers[3].market.exchange_id == EXCHANGE_ID
        assert str(futures_tickers[3].market.instrument) == str(INSTRUMENT)
        assert futures_tickers[3].data.price_mark == Decimal("68176.99")
        assert futures_tickers[3].data.price_index == Decimal("66708.56")
        assert futures_tickers[3].data.estimated_settlement_price == Decimal("0.00")
        assert futures_tickers[3].data.estimated_funding_rate is None
        assert futures_tickers[3].data.estimated_funding_rate_time is None
        assert futures_tickers[3].data.exchange_time is None  # exchange_time is never saved to DB
        assert futures_tickers[3].deduplication_time == datetime(2022, 5, 24, 1, 1, tzinfo=timezone.utc)
        assert futures_tickers[3].time == datetime(2022, 5, 24, 1, 1, tzinfo=timezone.utc)
        assert patcher_storage.save_daily_batch_export.call_count == 24
        _, kwargs = patcher_storage.save_daily_batch_export.call_args_list[0]
        assert kwargs["exchange_id"] == EXCHANGE_ID
        assert kwargs["data_type"] == DataType.TICKER_F
        assert kwargs["market_type"] == MarketType.FUTURES
        assert kwargs["env"] == HOST_ENV
        assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0)
        assert kwargs["end"] == datetime(2022, 5, 23, 1, 0)
        rows = kwargs["csv_data"].decode().splitlines()
        assert ["BTCUSD", "2022-05-23 00:00:00+00", "2022-05-23 00:00:00+00", "4.23", "4.23", "4.23", "0.0", ""] == rows[
            1
        ].split(",")[:-1]
        assert ["BTCUSD", "2022-05-23 00:01:00+00", "2022-05-23 00:01:00+00", "4.23", "4.23", "4.23", "0.0", ""] == rows[
            2
        ].split(",")[:-1]
        hours = 1
        for _, kwargs in patcher_storage.save_daily_batch_export.call_args_list[1:]:
            assert kwargs["begin"] == datetime(2022, 5, 23, 0, 0) + relativedelta(hours=hours)
            assert kwargs["end"] == datetime(2022, 5, 23, 1, 0) + relativedelta(hours=hours)
            assert_patch_csv_data_contains_only_header(kwargs["csv_data"].decode(), TABLE_COLS)
            hours += 1
